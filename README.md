# Kritrima AI - Advanced Local CLI Terminal LLM Interaction Environment

Kritrima AI is a sophisticated AI coding assistant that bridges the gap between simple code completion tools and full autonomous development. It's designed to handle real-world projects with large codebases while giving developers control over the implementation process.

## 🚀 Features

### Core Capabilities
- **Multi-Model Support**: OpenAI, Anthropic, Deepseek, and Ollama (local models)
- **Large Context Handling**: Up to 2M tokens of context directly
- **Autonomous Function Calling**: Execute tools in parallel with real-time streaming
- **Error Detection & Auto-Fix**: Automatically detect and fix bugs in your codebase
- **Diff Review Sandbox**: Review AI-generated changes before applying them
- **Interactive CLI**: Natural language interaction in your terminal

### AI Model Integrations
- **OpenAI**: GPT-4 Turbo, GPT-4, GPT-3.5 Turbo with function calling
- **Anthropic**: Claude-3 Opus, Sonnet, Haiku with tool use
- **Deepseek**: Deepseek Chat, Deepseek Coder with function calling
- **Ollama**: Local models (Llama2, CodeLlama, Mistral) with function calling

### Autonomous Tools
- **Shell Tool**: Execute bash/cmd commands safely in local environment
- **File Tool**: Read, write, manage files and directories
- **Edit Tool**: Precise line-based file editing with backup support
- **Write Tool**: Create new files with various modes and encodings
- **Grep Tool**: Advanced pattern searching with regex support
- **Web Tool**: Fetch web content, download files, make HTTP requests
- **Refactor Tool**: Extract functions, rename variables, optimize imports, generate tests
- **Monitor Tool**: Real-time system monitoring, performance analytics, health checks
- **AI Assistant Tool**: Code review, documentation generation, bug detection, optimization
- **Analysis Tool**: Code quality analysis, complexity metrics, security scanning
- **Diagnostics Tool**: System health checks, provider status, troubleshooting

### Advanced Features
- **Context Management**: Intelligent file indexing and token optimization
- **Error Detection**: Real-time scanning for syntax, security, and code quality issues
- **Diff Review**: Sandbox environment for reviewing changes before application
- **Plan Execution**: Break down complex tasks into executable steps
- **Streaming Responses**: Real-time AI responses with tool execution feedback
- **Modern UI/UX**: Dynamic themes, progress bars, animations, status indicators
- **Performance Monitoring**: Real-time system metrics, memory usage, CPU monitoring
- **Code Refactoring**: Intelligent code improvements, function extraction, optimization
- **Security Scanning**: Vulnerability detection, code security analysis
- **Documentation Generation**: Automatic API docs, README creation, code comments

## 📦 Installation

### Prerequisites
- Node.js 18 or higher
- npm or yarn package manager

### Install Dependencies
```bash
npm install
```

### Build the Project
```bash
npm run build
```

### Install Globally (Optional)
```bash
npm install -g .
```

## 🔧 Configuration

### Initialize Project
```bash
kritrima init
```

This creates:
- `.kritrima.json` - Configuration file
- `.env.example` - Environment variables template

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Deepseek API Key
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Ollama Base URL (for local models)
OLLAMA_BASE_URL=http://localhost:11434
```

### Configuration Options
```json
{
  "defaultModel": "gpt-4-turbo-preview",
  "autoFix": false,
  "contextSize": 2000000,
  "autonomyLevel": "guided",
  "logLevel": "info",
  "workingDirectory": ".",
  "excludePatterns": [
    "node_modules/**",
    "dist/**",
    "build/**",
    ".git/**"
  ]
}
```

## 🚀 Usage

### Start Interactive CLI
```bash
kritrima
```

### Command Line Options
```bash
kritrima [options]

Options:
  -m, --model <model>        AI model to use (default: gpt-4-turbo-preview)
  -d, --directory <path>     Working directory (default: current directory)
  -c, --context-size <size>  Context size in tokens (default: 2000000)
  -a, --autonomy <level>     Autonomy level: full|guided|manual (default: guided)
  --auto-fix                 Enable automatic error fixing
  --log-level <level>        Log level: debug|info|warn|error (default: info)
  --config <path>            Configuration file path
```

### Available Commands

#### In Interactive Mode
- `help` - Show available commands and tool reference
- `status` - Show current status and context
- `models` - List available AI models
- `model <name>` - Switch to a different model
- `context add <path>` - Add file/directory to context
- `context remove <path>` - Remove file from context
- `context list` - List files in context
- `scan` - Scan for errors in working directory
- `diagnostics` - Run comprehensive system diagnostics
- `diff` - Show diff review sandboxes
- `review` - Review recent changes and modifications
- `dashboard` - Switch to full dashboard view
- `compact` - Switch to compact header view
- `minimal` - Switch to minimal view
- `theme <name>` - Set UI theme (default, dark, matrix, ocean)
- `clear` - Clear current session
- `refresh` - Refresh display and status
- `exit/quit` - Exit the application

#### CLI Commands
```bash
# Scan directory for errors
kritrima scan --directory ./src --fix

# List available models
kritrima models

# Show current configuration
kritrima config
```

### Example Interactions

#### Code Analysis and Fixing
```
kritrima> Analyze my TypeScript project and fix any errors

🔧 Executing tools autonomously...
▶ shell: Scanning TypeScript files for errors
✓ Found 3 syntax errors and 5 warnings
▶ edit: Fixing missing semicolons in src/utils.ts
✓ Fixed 2 missing semicolons
▶ edit: Removing unused imports in src/components/Header.tsx
✓ Removed 3 unused imports
```

#### File Operations
```
kritrima> Create a new React component called UserProfile

🔧 Executing tools autonomously...
▶ write: Creating UserProfile component file
✓ Created src/components/UserProfile.tsx
▶ write: Creating UserProfile styles
✓ Created src/components/UserProfile.module.css
▶ edit: Adding export to index.ts
✓ Updated component exports
```

#### Search and Analysis
```
kritrima> Find all TODO comments in the codebase and create a summary

🔧 Executing tools autonomously...
▶ grep: Searching for TODO comments
✓ Found 12 TODO comments across 8 files
▶ write: Creating TODO summary report
✓ Created TODO_SUMMARY.md with categorized tasks
```

#### Code Refactoring
```
kritrima> Extract the authentication logic from src/auth.ts into separate functions

🔧 Executing tools autonomously...
▶ refactor: Analyzing authentication logic
✓ Identified 3 functions to extract
▶ refactor: Extracting validateUser function
✓ Created validateUser function with proper parameters
▶ refactor: Extracting generateToken function
✓ Created generateToken function with JWT logic
▶ refactor: Updating imports and exports
✓ Updated all import statements and module exports
```

#### System Monitoring
```
kritrima> Monitor system performance and check for memory leaks

🔧 Executing tools autonomously...
▶ monitor: Starting performance monitoring
✓ Monitoring CPU, memory, and disk usage for 30 seconds
▶ monitor: Analyzing memory patterns
✓ No memory leaks detected, average usage: 65%
▶ ai_assistant: Generating performance report
✓ Created performance_report.md with recommendations
```

#### AI-Powered Code Review
```
kritrima> Review the code in src/components for best practices and security issues

🔧 Executing tools autonomously...
▶ ai_assistant: Performing comprehensive code review
✓ Analyzed 15 component files for best practices
▶ ai_assistant: Checking for security vulnerabilities
✓ Found 2 potential XSS vulnerabilities in UserInput.tsx
▶ ai_assistant: Generating improvement suggestions
✓ Created code_review_report.md with 12 recommendations
```

#### Documentation Generation
```
kritrima> Generate comprehensive API documentation for the REST endpoints

🔧 Executing tools autonomously...
▶ analysis: Scanning for API endpoints
✓ Found 25 REST endpoints across 8 route files
▶ ai_assistant: Analyzing endpoint parameters and responses
✓ Extracted parameter schemas and response types
▶ ai_assistant: Generating OpenAPI specification
✓ Created api_documentation.yaml with complete API spec
▶ ai_assistant: Creating markdown documentation
✓ Created API_DOCS.md with usage examples
```

## 🛠️ Development

### Project Structure
```
src/
├── cli/           # CLI interface and interaction handling
│   ├── interface.ts      # Main CLI interface with enhanced commands
│   ├── modern-ui.ts      # Modern UI components and themes
│   └── dashboard.ts      # Real-time dashboard and status display
├── models/        # AI model integrations and providers
├── tools/         # Autonomous function calling tools
│   ├── file.ts           # File operations (read, write, copy, move)
│   ├── shell.ts          # Safe shell command execution
│   ├── edit.ts           # Precise file editing capabilities
│   ├── grep.ts           # Advanced pattern searching
│   ├── web.ts            # Web content fetching and HTTP requests
│   ├── analysis.ts       # Code quality and complexity analysis
│   ├── refactor.ts       # Code refactoring and optimization
│   ├── monitor.ts        # System monitoring and performance analytics
│   ├── ai-assistant.ts   # AI-powered code assistance
│   ├── diagnostics.ts    # System health checks and troubleshooting
│   └── manager.ts        # Tool registration and execution management
├── context/       # Context management and file indexing
├── core/          # Core application logic
│   ├── error-detector.ts # Error detection and auto-fixing
│   └── diff-reviewer.ts  # Change review and approval workflow
├── config/        # Configuration management
│   └── enhanced-features.ts # Advanced feature configuration
├── types/         # TypeScript type definitions
└── index.ts       # Main entry point
```

### Build and Development
```bash
# Development mode with hot reload
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Lint code
npm run lint

# Clean build directory
npm run clean
```

### Adding New Tools
1. Create tool class implementing the `Tool` interface
2. Register in `ToolManager`
3. Add tool description and parameters
4. Implement the `execute` method

Example:
```typescript
export class MyTool implements Tool {
  name = 'my-tool';
  description = 'Description of what this tool does';
  parameters = {
    type: 'object',
    properties: {
      input: { type: 'string', description: 'Input parameter' }
    },
    required: ['input']
  };

  async execute(args: { input: string }): Promise<ToolResult> {
    // Tool implementation
    return {
      success: true,
      output: 'Tool executed successfully'
    };
  }
}
```

## 🔒 Security

### Safe Command Execution
- Dangerous commands are blocked by default
- Sandboxed execution environment
- User confirmation for destructive operations

### API Key Security
- Environment variables for API keys
- No hardcoded secrets in codebase
- Automatic detection of potential secret leaks

## 📝 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

- GitHub Issues: Report bugs and request features
- Documentation: Check the docs/ directory for detailed guides
- Community: Join our Discord server for discussions

## 🎯 Roadmap

### ✅ Completed Features
- [x] Advanced code refactoring capabilities
- [x] Real-time system monitoring and performance analytics
- [x] AI-powered code review and documentation generation
- [x] Modern UI with themes and animations
- [x] Comprehensive tool ecosystem with 11+ tools
- [x] Enhanced error detection and auto-fixing
- [x] Diff review and change approval workflow

### 🚧 In Progress
- [ ] Plugin system for custom tools
- [ ] Advanced caching and performance optimization
- [ ] Enhanced security scanning and vulnerability detection

### 🔮 Future Plans
- [ ] Web interface for visual diff review
- [ ] Integration with popular IDEs (VS Code, IntelliJ)
- [ ] Team collaboration features and shared sessions
- [ ] Cloud deployment options and remote execution
- [ ] Advanced AI model fine-tuning for specific codebases
- [ ] Multi-language support beyond English
- [ ] Integration with version control systems (Git hooks)
- [ ] Advanced code generation templates and patterns
- [ ] Real-time collaborative coding sessions
- [ ] Enterprise features and access controls

---

**Kritrima AI** - Empowering developers with intelligent, autonomous coding assistance.
