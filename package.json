{"name": "kritrima-ai", "version": "1.0.0", "description": "Advanced Local CLI Terminal LLM Interaction Environment", "main": "dist/index.js", "bin": {"kritrima": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "jest"}, "keywords": ["ai", "cli", "llm", "coding-assistant", "autonomous", "function-calling"], "author": "Kritrima AI", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@types/node": "^20.10.0", "axios": "^1.6.2", "chalk": "^5.3.0", "chokidar": "^3.5.3", "commander": "^11.1.0", "diff": "^5.1.0", "dotenv": "^16.3.1", "fast-glob": "^3.3.2", "glob": "^10.3.10", "ignore": "^5.3.0", "inquirer": "^9.2.12", "minimatch": "^9.0.3", "node-fetch": "^3.3.2", "openai": "^4.20.1", "ora": "^7.0.1", "readline": "^1.3.0", "tree-kill": "^1.2.2", "ws": "^8.14.2", "yaml": "^2.3.4"}, "devDependencies": {"@types/diff": "^5.0.8", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.8", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "cross-env": "^7.0.3", "eslint": "^8.54.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "shx": "^0.4.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}