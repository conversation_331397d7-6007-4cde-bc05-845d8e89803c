import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
import { ContextFile, ContextManager as IContextManager, Message } from '../types';
import { SmartContextRetrieval } from './smart-retrieval';

export class ContextManager implements IContextManager {
  private files: Map<string, ContextFile> = new Map();
  private maxTokens: number;
  private readonly TOKENS_PER_CHAR = 0.25; // Approximate tokens per character
  private smartRetrieval: SmartContextRetrieval;
  private fileWatchers: Map<string, fs.FSWatcher> = new Map();
  private changeQueue: Set<string> = new Set();
  private processingChanges = false;

  constructor(maxTokens: number = 2000000) { // 2M tokens default
    this.maxTokens = maxTokens;
    this.smartRetrieval = new SmartContextRetrieval(this);
  }

  async addFile(filePath: string): Promise<void> {
    const resolvedPath = path.resolve(filePath);

    if (fs.statSync(resolvedPath).isDirectory()) {
      await this.addDirectory(resolvedPath);
    } else {
      await this.addSingleFile(resolvedPath);
    }

    this.optimize();
  }

  private async addDirectory(dirPath: string): Promise<void> {
    const patterns = [
      '**/*.ts', '**/*.js', '**/*.tsx', '**/*.jsx',
      '**/*.py', '**/*.java', '**/*.cpp', '**/*.c',
      '**/*.cs', '**/*.go', '**/*.rs', '**/*.php',
      '**/*.rb', '**/*.swift', '**/*.kt', '**/*.scala',
      '**/*.json', '**/*.yaml', '**/*.yml', '**/*.xml',
      '**/*.md', '**/*.txt', '**/*.sql', '**/*.sh',
      '**/*.dockerfile', '**/Dockerfile', '**/*.env'
    ];

    const ignorePatterns = [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/coverage/**',
      '**/*.log',
      '**/*.tmp',
      '**/*.temp'
    ];

    for (const pattern of patterns) {
      const files = await glob(pattern, {
        cwd: dirPath,
        ignore: ignorePatterns,
        absolute: true
      });

      for (const file of files) {
        try {
          await this.addSingleFile(file);
        } catch (error) {
          console.warn(`Failed to add file ${file}: ${error}`);
        }
      }
    }
  }

  private async addSingleFile(filePath: string): Promise<void> {
    try {
      const stats = fs.statSync(filePath);
      const content = fs.readFileSync(filePath, 'utf-8');

      // Skip binary files and very large files
      if (this.isBinaryFile(content) || stats.size > 1000000) { // 1MB limit per file
        return;
      }

      const contextFile: ContextFile = {
        path: filePath,
        content,
        size: stats.size,
        lastModified: stats.mtime,
        language: this.detectLanguage(filePath)
      };

      this.files.set(filePath, contextFile);
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error}`);
    }
  }

  removeFile(filePath: string): void {
    const resolvedPath = path.resolve(filePath);
    this.files.delete(resolvedPath);
  }

  getContext(): ContextFile[] {
    return Array.from(this.files.values());
  }

  getTotalTokens(): number {
    let totalChars = 0;
    for (const file of this.files.values()) {
      totalChars += file.content.length;
    }
    return Math.ceil(totalChars * this.TOKENS_PER_CHAR);
  }

  optimize(): void {
    const currentTokens = this.getTotalTokens();

    if (currentTokens <= this.maxTokens) {
      return;
    }

    // Sort files by importance (smaller files, recently modified, code files first)
    const sortedFiles = Array.from(this.files.entries()).sort(([, a], [, b]) => {
      // Prioritize code files
      const aIsCode = this.isCodeFile(a.path);
      const bIsCode = this.isCodeFile(b.path);
      if (aIsCode !== bIsCode) {
        return bIsCode ? 1 : -1;
      }

      // Prioritize recently modified files
      const timeDiff = b.lastModified.getTime() - a.lastModified.getTime();
      if (Math.abs(timeDiff) > 86400000) { // 1 day
        return timeDiff > 0 ? 1 : -1;
      }

      // Prioritize smaller files
      return a.size - b.size;
    });

    // Remove files until we're under the token limit
    let tokensToRemove = currentTokens - this.maxTokens;
    for (const [filePath, file] of sortedFiles.reverse()) {
      if (tokensToRemove <= 0) break;

      const fileTokens = Math.ceil(file.content.length * this.TOKENS_PER_CHAR);
      this.files.delete(filePath);
      tokensToRemove -= fileTokens;
    }
  }

  async loadWorkingDirectory(workingDir: string): Promise<void> {
    if (!fs.existsSync(workingDir)) {
      throw new Error(`Working directory does not exist: ${workingDir}`);
    }

    await this.addDirectory(workingDir);
  }

  private isBinaryFile(content: string): boolean {
    // Check for null bytes which indicate binary content
    return content.includes('\0');
  }

  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php',
      '.rb': 'ruby',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.json': 'json',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.xml': 'xml',
      '.md': 'markdown',
      '.sql': 'sql',
      '.sh': 'bash',
      '.dockerfile': 'dockerfile'
    };

    return languageMap[ext] || 'text';
  }

  private isCodeFile(filePath: string): boolean {
    const codeExtensions = [
      '.ts', '.tsx', '.js', '.jsx', '.py', '.java',
      '.cpp', '.c', '.cs', '.go', '.rs', '.php',
      '.rb', '.swift', '.kt', '.scala'
    ];

    const ext = path.extname(filePath).toLowerCase();
    return codeExtensions.includes(ext);
  }

  // Smart context retrieval for LLM queries
  async getRelevantContext(
    userMessage: string,
    conversationHistory: Message[] = [],
    maxTokens?: number
  ): Promise<{
    files: ContextFile[];
    totalTokens: number;
    reasoning: string;
  }> {
    const effectiveMaxTokens = maxTokens || Math.floor(this.maxTokens * 0.8); // Reserve 20% for response

    const result = await this.smartRetrieval.getRelevantContext({
      userMessage,
      conversationHistory,
      currentFiles: this.getContext(),
      maxTokens: effectiveMaxTokens
    });

    return {
      files: result.selectedFiles,
      totalTokens: result.totalTokens,
      reasoning: result.reasoning
    };
  }

  // Real-time file monitoring
  startMonitoring(directoryPath: string): void {
    if (this.fileWatchers.has(directoryPath)) {
      return; // Already monitoring
    }

    try {
      const watcher = fs.watch(directoryPath, { recursive: true }, (eventType, filename) => {
        if (filename && (eventType === 'change' || eventType === 'rename')) {
          const fullPath = path.join(directoryPath, filename);
          this.queueFileChange(fullPath);
        }
      });

      this.fileWatchers.set(directoryPath, watcher);
      console.log(`📁 Started monitoring: ${directoryPath}`);
    } catch (error) {
      console.warn(`Failed to start monitoring ${directoryPath}:`, error);
    }
  }

  stopMonitoring(directoryPath?: string): void {
    if (directoryPath) {
      const watcher = this.fileWatchers.get(directoryPath);
      if (watcher) {
        watcher.close();
        this.fileWatchers.delete(directoryPath);
        console.log(`📁 Stopped monitoring: ${directoryPath}`);
      }
    } else {
      // Stop all monitoring
      for (const [path, watcher] of this.fileWatchers) {
        watcher.close();
        console.log(`📁 Stopped monitoring: ${path}`);
      }
      this.fileWatchers.clear();
    }
  }

  private queueFileChange(filePath: string): void {
    this.changeQueue.add(filePath);

    if (!this.processingChanges) {
      // Debounce file changes
      setTimeout(() => this.processFileChanges(), 1000);
      this.processingChanges = true;
    }
  }

  private async processFileChanges(): Promise<void> {
    const changes = Array.from(this.changeQueue);
    this.changeQueue.clear();
    this.processingChanges = false;

    for (const filePath of changes) {
      try {
        if (fs.existsSync(filePath) && this.shouldIncludeFile(filePath)) {
          await this.addSingleFile(filePath);
          console.log(`📝 Updated: ${path.basename(filePath)}`);
        } else if (this.files.has(filePath)) {
          this.files.delete(filePath);
          console.log(`🗑️  Removed: ${path.basename(filePath)}`);
        }
      } catch (error) {
        // Silent fail for individual file updates
      }
    }

    // Re-optimize context if needed
    this.optimize();
  }

  // Enhanced file filtering
  private shouldIncludeFile(filePath: string): boolean {
    const filename = path.basename(filePath);
    const ext = path.extname(filePath).toLowerCase();

    // Skip hidden files and directories
    if (filename.startsWith('.')) return false;

    // Skip common build/cache directories
    const skipDirs = ['node_modules', 'dist', 'build', '.git', '.vscode', 'coverage', '__pycache__'];
    if (skipDirs.some(dir => filePath.includes(dir))) return false;

    // Include common source file extensions
    const includeExts = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs', '.json', '.md', '.yml', '.yaml', '.xml', '.html', '.css', '.scss', '.less'];
    if (!includeExts.includes(ext)) return false;

    // Skip very large files (>1MB)
    try {
      const stats = fs.statSync(filePath);
      if (stats.size > 1000000) return false;
    } catch {
      return false;
    }

    return true;
  }

  // Get context summary for AI models
  getContextSummary(): string {
    const files = this.getContext();
    const summary = {
      totalFiles: files.length,
      totalTokens: this.getTotalTokens(),
      languages: {} as Record<string, number>,
      recentFiles: files
        .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
        .slice(0, 10)
        .map(f => ({ path: f.path, language: f.language }))
    };

    // Count files by language
    for (const file of files) {
      const lang = file.language || 'unknown';
      summary.languages[lang] = (summary.languages[lang] || 0) + 1;
    }

    return JSON.stringify(summary, null, 2);
  }

  // Get specific files by pattern
  getFilesByPattern(pattern: string): ContextFile[] {
    const regex = new RegExp(pattern, 'i');
    return this.getContext().filter(file =>
      regex.test(file.path) || regex.test(file.content)
    );
  }

  // Get files by language
  getFilesByLanguage(language: string): ContextFile[] {
    return this.getContext().filter(file => file.language === language);
  }
}
