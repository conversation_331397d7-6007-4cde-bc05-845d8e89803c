import * as fs from 'fs';
import * as path from 'path';
import { ContextFile, Message } from '../types';
import { ContextManager } from './manager';

interface FileRelevanceScore {
  file: ContextFile;
  score: number;
  reasons: string[];
}

interface ContextQuery {
  userMessage: string;
  conversationHistory: Message[];
  currentFiles: ContextFile[];
  maxTokens: number;
}

interface SmartContextResult {
  selectedFiles: ContextFile[];
  totalTokens: number;
  relevanceScores: FileRelevanceScore[];
  reasoning: string;
}

export class SmartContextRetrieval {
  private contextManager: ContextManager;
  private readonly TOKENS_PER_CHAR = 0.25;
  private readonly MIN_RELEVANCE_SCORE = 0.3;
  private readonly MAX_FILES_PER_REQUEST = 20;

  constructor(contextManager: ContextManager) {
    this.contextManager = contextManager;
  }

  async getRelevantContext(query: ContextQuery): Promise<SmartContextResult> {
    const { userMessage, conversationHistory, currentFiles, maxTokens } = query;
    
    // Calculate relevance scores for all files
    const relevanceScores = await this.calculateRelevanceScores(
      userMessage,
      conversationHistory,
      currentFiles
    );

    // Sort by relevance score
    const sortedFiles = relevanceScores
      .filter(score => score.score >= this.MIN_RELEVANCE_SCORE)
      .sort((a, b) => b.score - a.score)
      .slice(0, this.MAX_FILES_PER_REQUEST);

    // Select files within token limit
    const selectedFiles = this.selectFilesWithinTokenLimit(sortedFiles, maxTokens);
    
    const totalTokens = this.calculateTotalTokens(selectedFiles.map(s => s.file));
    
    const reasoning = this.generateReasoningExplanation(selectedFiles, userMessage);

    return {
      selectedFiles: selectedFiles.map(s => s.file),
      totalTokens,
      relevanceScores: sortedFiles,
      reasoning
    };
  }

  private async calculateRelevanceScores(
    userMessage: string,
    conversationHistory: Message[],
    files: ContextFile[]
  ): Promise<FileRelevanceScore[]> {
    const scores: FileRelevanceScore[] = [];
    
    // Extract keywords and context from user message and conversation
    const keywords = this.extractKeywords(userMessage);
    const recentlyMentionedFiles = this.extractMentionedFiles(conversationHistory);
    const codePatterns = this.extractCodePatterns(userMessage);
    
    for (const file of files) {
      const score = await this.calculateFileRelevance(
        file,
        keywords,
        recentlyMentionedFiles,
        codePatterns,
        userMessage
      );
      
      scores.push(score);
    }
    
    return scores;
  }

  private async calculateFileRelevance(
    file: ContextFile,
    keywords: string[],
    recentlyMentionedFiles: string[],
    codePatterns: string[],
    userMessage: string
  ): Promise<FileRelevanceScore> {
    let score = 0;
    const reasons: string[] = [];
    
    // Base score for file type and language
    score += this.getLanguageRelevanceScore(file, userMessage);
    
    // Keyword matching in filename
    const filename = path.basename(file.path).toLowerCase();
    for (const keyword of keywords) {
      if (filename.includes(keyword.toLowerCase())) {
        score += 0.3;
        reasons.push(`Filename contains keyword: ${keyword}`);
      }
    }
    
    // Keyword matching in file content
    const content = file.content.toLowerCase();
    for (const keyword of keywords) {
      const keywordCount = (content.match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
      if (keywordCount > 0) {
        score += Math.min(keywordCount * 0.1, 0.5);
        reasons.push(`Content contains keyword "${keyword}" ${keywordCount} times`);
      }
    }
    
    // Recently mentioned files get higher priority
    if (recentlyMentionedFiles.some(mentioned => file.path.includes(mentioned))) {
      score += 0.4;
      reasons.push('Recently mentioned in conversation');
    }
    
    // Code pattern matching
    for (const pattern of codePatterns) {
      if (content.includes(pattern)) {
        score += 0.2;
        reasons.push(`Contains code pattern: ${pattern}`);
      }
    }
    
    // File recency (more recently modified files get slight boost)
    const daysSinceModified = (Date.now() - file.lastModified.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceModified < 7) {
      score += 0.1;
      reasons.push('Recently modified');
    }
    
    // File size consideration (very large files get penalty, very small files get penalty)
    if (file.size > 100000) {
      score -= 0.1;
      reasons.push('Large file size penalty');
    } else if (file.size < 100) {
      score -= 0.05;
      reasons.push('Very small file penalty');
    }
    
    // Boost for certain important file types
    if (this.isImportantFileType(file)) {
      score += 0.2;
      reasons.push('Important file type');
    }
    
    return {
      file,
      score: Math.max(0, Math.min(1, score)), // Clamp between 0 and 1
      reasons
    };
  }

  private extractKeywords(text: string): string[] {
    // Extract meaningful keywords from user message
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    // Filter out common words
    const stopWords = new Set(['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way', 'she', 'use', 'her', 'many', 'oil', 'sit', 'word', 'long', 'down', 'side', 'been', 'call', 'come', 'each', 'find', 'have', 'here', 'just', 'like', 'look', 'made', 'make', 'more', 'move', 'must', 'name', 'over', 'said', 'same', 'some', 'take', 'than', 'that', 'them', 'this', 'time', 'very', 'well', 'were', 'what', 'when', 'will', 'with', 'work', 'your']);
    
    return words.filter(word => !stopWords.has(word));
  }

  private extractMentionedFiles(conversationHistory: Message[]): string[] {
    const mentionedFiles: string[] = [];
    
    // Look for file paths in recent conversation
    const recentMessages = conversationHistory.slice(-10);
    
    for (const message of recentMessages) {
      const content = message.content || '';
      
      // Extract file paths (simple pattern matching)
      const filePathPattern = /[\w\/\\.-]+\.(js|ts|jsx|tsx|py|java|cpp|c|h|cs|php|rb|go|rs|json|md|txt|yml|yaml|xml|html|css|scss|less)/gi;
      const matches = content.match(filePathPattern);
      
      if (matches) {
        mentionedFiles.push(...matches);
      }
    }
    
    return mentionedFiles;
  }

  private extractCodePatterns(text: string): string[] {
    const patterns: string[] = [];
    
    // Extract function names, class names, variable names
    const functionPattern = /function\s+(\w+)|(\w+)\s*\(/g;
    const classPattern = /class\s+(\w+)/g;
    const variablePattern = /(?:const|let|var)\s+(\w+)/g;
    
    let match;
    
    while ((match = functionPattern.exec(text)) !== null) {
      patterns.push(match[1] || match[2]);
    }
    
    while ((match = classPattern.exec(text)) !== null) {
      patterns.push(match[1]);
    }
    
    while ((match = variablePattern.exec(text)) !== null) {
      patterns.push(match[1]);
    }
    
    return patterns.filter(p => p && p.length > 2);
  }

  private getLanguageRelevanceScore(file: ContextFile, userMessage: string): number {
    const message = userMessage.toLowerCase();
    const language = file.language || '';
    
    // Check if user message mentions specific languages or technologies
    const languageBoosts: Record<string, string[]> = {
      'javascript': ['javascript', 'js', 'node', 'npm', 'react', 'vue', 'angular'],
      'typescript': ['typescript', 'ts', 'type', 'interface'],
      'python': ['python', 'py', 'django', 'flask', 'pandas'],
      'java': ['java', 'spring', 'maven', 'gradle'],
      'cpp': ['c++', 'cpp', 'c', 'cmake'],
      'csharp': ['c#', 'csharp', 'dotnet', '.net'],
      'go': ['go', 'golang'],
      'rust': ['rust', 'cargo'],
      'php': ['php', 'laravel', 'symfony'],
      'ruby': ['ruby', 'rails', 'gem']
    };
    
    for (const [lang, keywords] of Object.entries(languageBoosts)) {
      if (language === lang && keywords.some(keyword => message.includes(keyword))) {
        return 0.3;
      }
    }
    
    return 0.1; // Base score for any file
  }

  private isImportantFileType(file: ContextFile): boolean {
    const filename = path.basename(file.path).toLowerCase();
    const importantFiles = [
      'package.json', 'tsconfig.json', 'webpack.config.js', 'babel.config.js',
      'dockerfile', 'docker-compose.yml', 'readme.md', 'index.js', 'index.ts',
      'main.js', 'main.ts', 'app.js', 'app.ts', 'server.js', 'server.ts'
    ];
    
    return importantFiles.includes(filename) || 
           filename.includes('config') || 
           filename.includes('index') ||
           filename.includes('main');
  }

  private selectFilesWithinTokenLimit(
    sortedFiles: FileRelevanceScore[],
    maxTokens: number
  ): FileRelevanceScore[] {
    const selected: FileRelevanceScore[] = [];
    let currentTokens = 0;
    
    for (const fileScore of sortedFiles) {
      const fileTokens = this.calculateFileTokens(fileScore.file);
      
      if (currentTokens + fileTokens <= maxTokens) {
        selected.push(fileScore);
        currentTokens += fileTokens;
      } else {
        break;
      }
    }
    
    return selected;
  }

  private calculateFileTokens(file: ContextFile): number {
    return Math.ceil(file.content.length * this.TOKENS_PER_CHAR);
  }

  private calculateTotalTokens(files: ContextFile[]): number {
    return files.reduce((total, file) => total + this.calculateFileTokens(file), 0);
  }

  private generateReasoningExplanation(
    selectedFiles: FileRelevanceScore[],
    userMessage: string
  ): string {
    if (selectedFiles.length === 0) {
      return 'No relevant files found for the current query.';
    }
    
    const topFiles = selectedFiles.slice(0, 3);
    const explanations = topFiles.map(fileScore => {
      const filename = path.basename(fileScore.file.path);
      const score = (fileScore.score * 100).toFixed(0);
      const topReasons = fileScore.reasons.slice(0, 2).join(', ');
      return `${filename} (${score}% relevant: ${topReasons})`;
    });
    
    return `Selected ${selectedFiles.length} most relevant files: ${explanations.join('; ')}`;
  }
}
