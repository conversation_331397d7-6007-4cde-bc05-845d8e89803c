import { OpenAI } from 'openai';
import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';
import axios from 'axios';

export class DeepseekProvider {
  private client: OpenAI;
  private maxRetries: number = 5; // Increased retries
  private baseDelay: number = 1000; // 1 second
  private maxDelay: number = 30000; // 30 seconds max delay
  private connectionHealthy: boolean = false;
  private lastHealthCheck: number = 0;
  private healthCheckInterval: number = 300000; // 5 minutes

  constructor(apiKey: string, baseURL?: string) {
    const finalBaseURL = baseURL || process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1';

    this.client = new OpenAI({
      apiKey: apiKey,
      baseURL: finalBaseURL,
      timeout: 120000, // 2 minutes timeout
      maxRetries: 0, // We'll handle retries manually for better control
      defaultHeaders: {
        'User-Agent': 'KritrimaAI/1.0.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    // Initialize connection health check
    this.initializeHealthCheck();
  }

  private async initializeHealthCheck(): Promise<void> {
    try {
      await this.checkConnectionHealth();
    } catch (error) {
      console.warn('Initial Deepseek health check failed, will retry during operation');
    }
  }

  async generateResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig
  ): Promise<StreamingResponse> {
    // Validate message sequence before conversion
    this.validateMessageSequence(messages);

    // Calculate token count and optimize if needed
    const optimizedMessages = await this.optimizeMessagesForTokenLimit(messages, config.maxTokens || 4096);
    const openaiMessages = this.convertMessages(optimizedMessages);
    const openaiTools = this.convertTools(tools);

    return await this.executeWithRetry(async () => {
      const response = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        tools: openaiTools.length > 0 ? openaiTools : undefined,
        tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
        temperature: config.temperature,
        max_tokens: Math.min(config.maxTokens || 4096, 4096), // Ensure we don't exceed DeepSeek limits
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stream: false
      });

      const choice = response.choices[0];
      if (!choice) {
        throw new Error('No response from Deepseek');
      }

      return {
        content: choice.message.content || '',
        done: true,
        toolCalls: choice.message.tool_calls?.map(tc => ({
          id: tc.id,
          type: 'function' as const,
          function: {
            name: tc.function.name,
            arguments: tc.function.arguments
          }
        }))
      };
    });
  }

  async streamResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    // Validate message sequence before conversion
    this.validateMessageSequence(messages);

    // Calculate token count and optimize if needed
    const optimizedMessages = await this.optimizeMessagesForTokenLimit(messages, config.maxTokens || 4096);
    const openaiMessages = this.convertMessages(optimizedMessages);
    const openaiTools = this.convertTools(tools);

    await this.executeWithRetry(async () => {
      const stream = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        tools: openaiTools.length > 0 ? openaiTools : undefined,
        tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
        temperature: config.temperature,
        max_tokens: Math.min(config.maxTokens || 4096, 4096), // Ensure we don't exceed DeepSeek limits
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stream: true
      });

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;
        if (delta?.content) {
          onChunk(delta.content);
        }
      }
    });
  }

  // Token optimization methods
  private async optimizeMessagesForTokenLimit(messages: Message[], maxTokens: number): Promise<Message[]> {
    const DEEPSEEK_MAX_CONTEXT = 65536; // DeepSeek's actual context limit
    const SAFETY_MARGIN = 4096; // Reserve tokens for response
    const effectiveLimit = Math.min(maxTokens, DEEPSEEK_MAX_CONTEXT - SAFETY_MARGIN);

    let totalTokens = this.estimateTokenCount(messages);

    if (totalTokens <= effectiveLimit) {
      return messages;
    }

    console.warn(`Token count (${totalTokens}) exceeds limit (${effectiveLimit}). Optimizing context...`);

    // Keep system message and last few user/assistant messages
    const systemMessages = messages.filter(m => m.role === 'system');
    const conversationMessages = messages.filter(m => m.role !== 'system');

    // Start with system messages
    let optimizedMessages = [...systemMessages];
    let currentTokens = this.estimateTokenCount(optimizedMessages);

    // Add conversation messages from most recent, prioritizing user messages
    const reversedConversation = [...conversationMessages].reverse();

    for (const message of reversedConversation) {
      const messageTokens = this.estimateTokenCount([message]);

      if (currentTokens + messageTokens <= effectiveLimit) {
        optimizedMessages.push(message);
        currentTokens += messageTokens;
      } else {
        // Try to truncate the message content if it's too long
        const truncatedMessage = this.truncateMessage(message, effectiveLimit - currentTokens);
        if (truncatedMessage) {
          optimizedMessages.push(truncatedMessage);
          break;
        }
      }
    }

    // Restore chronological order (except system messages)
    const finalSystemMessages = optimizedMessages.filter(m => m.role === 'system');
    const finalConversationMessages = optimizedMessages
      .filter(m => m.role !== 'system')
      .reverse();

    const result = [...finalSystemMessages, ...finalConversationMessages];
    const finalTokenCount = this.estimateTokenCount(result);

    console.log(`Context optimized: ${totalTokens} → ${finalTokenCount} tokens (${result.length} messages)`);

    return result;
  }

  private estimateTokenCount(messages: Message[]): number {
    // Rough estimation: ~4 characters per token for English text
    const CHARS_PER_TOKEN = 4;

    return messages.reduce((total, message) => {
      const contentLength = (message.content || '').length;
      const nameLength = (message.name || '').length;
      const functionCallLength = message.function_call ?
        JSON.stringify(message.function_call).length : 0;
      const toolCallsLength = message.tool_calls ?
        JSON.stringify(message.tool_calls).length : 0;

      const totalChars = contentLength + nameLength + functionCallLength + toolCallsLength;
      return total + Math.ceil(totalChars / CHARS_PER_TOKEN) + 10; // +10 for message overhead
    }, 0);
  }

  private truncateMessage(message: Message, maxTokens: number): Message | null {
    if (maxTokens < 50) return null; // Not worth truncating if too small

    const maxChars = (maxTokens - 10) * 4; // Reserve tokens for overhead

    if ((message.content || '').length <= maxChars) {
      return message;
    }

    // Truncate content but keep important parts
    const content = message.content || '';
    const truncatedContent = content.substring(0, maxChars - 50) + '... [truncated]';

    return {
      ...message,
      content: truncatedContent
    };
  }

  private convertMessages(messages: Message[]): any[] {
    return messages.map(msg => {
      switch (msg.role) {
        case 'system':
          return {
            role: 'system',
            content: msg.content
          };
        case 'user':
          return {
            role: 'user',
            content: msg.content
          };
        case 'assistant':
          const assistantMsg: any = {
            role: 'assistant',
            content: msg.content
          };

          if (msg.tool_calls) {
            assistantMsg.tool_calls = msg.tool_calls.map(tc => ({
              id: tc.id,
              type: 'function',
              function: {
                name: tc.function.name,
                arguments: tc.function.arguments
              }
            }));
          }

          return assistantMsg;
        case 'function':
          return {
            role: 'tool',
            tool_call_id: msg.name || 'unknown', // msg.name should contain the tool_call_id
            content: msg.content
          };
        default:
          throw new Error(`Unsupported message role: ${msg.role}`);
      }
    });
  }

  private convertTools(tools: Tool[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  private validateMessageSequence(messages: Message[]): void {
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];

      // Check if tool result messages follow assistant messages with tool_calls
      if (message.role === 'function') {
        // Find the preceding assistant message with tool_calls
        let foundToolCall = false;
        for (let j = i - 1; j >= 0; j--) {
          const prevMessage = messages[j];
          if (prevMessage.role === 'assistant' && prevMessage.tool_calls) {
            // Check if this tool result corresponds to one of the tool calls
            const toolCallIds = prevMessage.tool_calls.map(tc => tc.id);
            if (toolCallIds.includes(message.name || '')) {
              foundToolCall = true;
              break;
            }
          }
          // Stop looking if we hit another assistant message without tool_calls
          if (prevMessage.role === 'assistant' && !prevMessage.tool_calls) {
            break;
          }
        }

        if (!foundToolCall) {
          console.warn(`Warning: Tool result message without corresponding tool call: ${message.name}`);
          // Don't throw error, just warn - let the API handle it
        }
      }
    }
  }

  private async checkConnectionHealth(): Promise<boolean> {
    const now = Date.now();

    // Use cached result if recent
    if (this.connectionHealthy && (now - this.lastHealthCheck) < this.healthCheckInterval) {
      return this.connectionHealthy;
    }

    try {
      // Test basic connectivity first
      await axios.get('https://api.deepseek.com', {
        timeout: 10000,
        validateStatus: () => true // Accept any status code
      });

      // Test API endpoint
      await this.client.chat.completions.create({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: 'ping' }],
        max_tokens: 1
      });

      this.connectionHealthy = true;
      this.lastHealthCheck = now;
      return true;
    } catch (error) {
      this.connectionHealthy = false;
      this.lastHealthCheck = now;
      console.warn(`Deepseek health check failed: ${error}`);
      return false;
    }
  }

  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    // Check connection health before starting
    if (!this.connectionHealthy) {
      await this.checkConnectionHealth();
    }

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain types of errors
        if (this.isNonRetryableError(error)) {
          throw this.enhanceError(error);
        }

        // If this was the last attempt, throw the error
        if (attempt === this.maxRetries) {
          throw this.enhanceError(error);
        }

        // Calculate delay with exponential backoff and jitter
        const baseDelay = this.baseDelay * Math.pow(2, attempt);
        const jitter = Math.random() * 1000; // Add randomness to prevent thundering herd
        const delay = Math.min(baseDelay + jitter, this.maxDelay);

        console.warn(`Deepseek API attempt ${attempt + 1} failed, retrying in ${Math.round(delay)}ms...`);

        // Mark connection as unhealthy on network errors
        if (this.isNetworkError(error)) {
          this.connectionHealthy = false;
        }

        await this.sleep(delay);
      }
    }

    throw this.enhanceError(lastError!);
  }

  private isNetworkError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    return errorMessage.includes('enotfound') ||
           errorMessage.includes('econnrefused') ||
           errorMessage.includes('timeout') ||
           errorMessage.includes('network') ||
           errorMessage.includes('connection');
  }

  private isNonRetryableError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const errorCode = error?.code;

    // Don't retry on authentication errors
    if (errorMessage.includes('unauthorized') || errorMessage.includes('invalid api key')) {
      return true;
    }

    // Don't retry on quota exceeded errors
    if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      return true;
    }

    // Don't retry on invalid request errors
    if (errorMessage.includes('bad request') || errorCode === 400) {
      return true;
    }

    return false;
  }

  private enhanceError(error: any): Error {
    const errorMessage = error?.message || 'Unknown error';
    const errorCode = error?.code;

    // Provide more helpful error messages
    if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNREFUSED')) {
      return new Error('Connection error: Unable to reach Deepseek API. Please check your internet connection and try again.');
    }

    if (errorMessage.includes('timeout')) {
      return new Error('Connection timeout: The request to Deepseek API timed out. Please try again.');
    }

    if (errorMessage.includes('unauthorized') || errorMessage.includes('invalid api key')) {
      return new Error('Authentication error: Invalid Deepseek API key. Please check your DEEPSEEK_API_KEY in the .env file.');
    }

    if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      return new Error('Rate limit exceeded: You have exceeded your Deepseek API quota. Please wait or upgrade your plan.');
    }

    if (errorCode === 400) {
      return new Error(`Bad request: ${errorMessage}. Please check your request parameters.`);
    }

    return new Error(`Deepseek API error: ${errorMessage}`);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async testConnection(): Promise<boolean> {
    try {
      // Force a fresh health check
      this.connectionHealthy = false;
      return await this.checkConnectionHealth();
    } catch (error) {
      console.warn(`Deepseek connection test failed: ${error}`);
      return false;
    }
  }

  // Get connection status without testing
  getConnectionStatus(): { healthy: boolean; lastCheck: Date | null } {
    return {
      healthy: this.connectionHealthy,
      lastCheck: this.lastHealthCheck > 0 ? new Date(this.lastHealthCheck) : null
    };
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data.map(model => model.id).sort();
    } catch (error) {
      return ['deepseek-chat', 'deepseek-coder']; // fallback;
    }
  }
}
