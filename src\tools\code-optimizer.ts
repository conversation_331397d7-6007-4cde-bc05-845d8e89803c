import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { Tool, ToolResult } from '../types';
import { glob } from 'glob';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

interface OptimizationRule {
  id: string;
  name: string;
  description: string;
  pattern: RegExp;
  replacement: string | ((match: string, ...groups: string[]) => string);
  category: 'performance' | 'readability' | 'security' | 'maintainability' | 'best-practices';
  severity: 'low' | 'medium' | 'high' | 'critical';
  language: string[];
  enabled: boolean;
}

interface OptimizationSuggestion {
  file: string;
  line: number;
  column: number;
  rule: OptimizationRule;
  originalCode: string;
  optimizedCode: string;
  impact: string;
  confidence: number;
  autoFixable: boolean;
}

interface CodeMetrics {
  file: string;
  lines: number;
  complexity: {
    cyclomatic: number;
    cognitive: number;
    halstead: {
      vocabulary: number;
      length: number;
      volume: number;
      difficulty: number;
      effort: number;
    };
  };
  maintainability: {
    index: number;
    techDebt: string;
  };
  duplicates: {
    blocks: number;
    lines: number;
    percentage: number;
  };
  issues: {
    bugs: number;
    vulnerabilities: number;
    codeSmells: number;
    hotspots: number;
  };
}

export class CodeOptimizerTool implements Tool {
  name = 'code_optimizer';
  description = 'Advanced code optimization with performance analysis, refactoring suggestions, and automated improvements';

  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['analyze', 'optimize', 'refactor', 'metrics', 'suggestions', 'auto_fix', 'benchmark', 'compare'],
        description: 'Code optimization action to perform'
      },
      path: {
        type: 'string',
        description: 'File or directory path to analyze/optimize'
      },
      language: {
        type: 'string',
        enum: ['javascript', 'typescript', 'python', 'java', 'cpp', 'csharp', 'php', 'ruby', 'go', 'rust', 'auto'],
        description: 'Programming language (default: auto-detect)'
      },
      rules: {
        type: 'array',
        items: { type: 'string' },
        description: 'Specific optimization rules to apply'
      },
      categories: {
        type: 'array',
        items: { type: 'string' },
        description: 'Optimization categories to focus on'
      },
      severity: {
        type: 'string',
        enum: ['low', 'medium', 'high', 'critical'],
        description: 'Minimum severity level (default: medium)'
      },
      auto_fix: {
        type: 'boolean',
        description: 'Automatically apply safe optimizations (default: false)'
      },
      backup: {
        type: 'boolean',
        description: 'Create backup before applying changes (default: true)'
      },
      format: {
        type: 'string',
        enum: ['json', 'table', 'detailed', 'summary', 'diff'],
        description: 'Output format (default: summary)'
      },
      recursive: {
        type: 'boolean',
        description: 'Process directories recursively (default: true)'
      },
      exclude_patterns: {
        type: 'array',
        items: { type: 'string' },
        description: 'File patterns to exclude'
      }
    },
    required: ['action', 'path']
  };

  private optimizationRules: Map<string, OptimizationRule> = new Map();
  private codeMetricsCache: Map<string, CodeMetrics> = new Map();

  constructor() {
    this.initializeOptimizationRules();
  }

  async execute(args: {
    action: string;
    path: string;
    language?: string;
    rules?: string[];
    categories?: string[];
    severity?: string;
    auto_fix?: boolean;
    backup?: boolean;
    format?: string;
    recursive?: boolean;
    exclude_patterns?: string[];
  }): Promise<ToolResult> {
    try {
      const {
        action,
        path: targetPath,
        language = 'auto',
        rules,
        categories,
        severity = 'medium',
        auto_fix = false,
        backup = true,
        format = 'summary',
        recursive = true,
        exclude_patterns = ['node_modules/**', 'dist/**', 'build/**', '.git/**']
      } = args;

      switch (action) {
        case 'analyze':
          return await this.analyzeCode(targetPath, {
            language,
            recursive,
            exclude_patterns,
            format
          });

        case 'optimize':
          return await this.optimizeCode(targetPath, {
            language,
            rules,
            categories,
            severity,
            auto_fix,
            backup,
            format,
            recursive,
            exclude_patterns
          });

        case 'refactor':
          return await this.refactorCode(targetPath, {
            language,
            format,
            recursive,
            exclude_patterns
          });

        case 'metrics':
          return await this.calculateMetrics(targetPath, {
            language,
            format,
            recursive,
            exclude_patterns
          });

        case 'suggestions':
          return await this.generateSuggestions(targetPath, {
            language,
            categories,
            severity,
            format,
            recursive,
            exclude_patterns
          });

        case 'auto_fix':
          return await this.autoFixIssues(targetPath, {
            language,
            rules,
            backup,
            format,
            recursive,
            exclude_patterns
          });

        case 'benchmark':
          return await this.benchmarkCode(targetPath, {
            language,
            format
          });

        case 'compare':
          return await this.compareOptimizations(targetPath, {
            format
          });

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Code optimization failed'
      };
    }
  }

  private initializeOptimizationRules(): void {
    // JavaScript/TypeScript optimization rules
    this.optimizationRules.set('js_const_let', {
      id: 'js_const_let',
      name: 'Use const/let instead of var',
      description: 'Replace var declarations with const or let for better scoping',
      pattern: /\bvar\s+(\w+)/g,
      replacement: 'let $1',
      category: 'best-practices',
      severity: 'medium',
      language: ['javascript', 'typescript'],
      enabled: true
    });

    this.optimizationRules.set('js_arrow_functions', {
      id: 'js_arrow_functions',
      name: 'Use arrow functions for callbacks',
      description: 'Replace function expressions with arrow functions for better readability',
      pattern: /function\s*\(([^)]*)\)\s*\{([^}]*)\}/g,
      replacement: '($1) => {$2}',
      category: 'readability',
      severity: 'low',
      language: ['javascript', 'typescript'],
      enabled: true
    });

    this.optimizationRules.set('js_template_literals', {
      id: 'js_template_literals',
      name: 'Use template literals for string concatenation',
      description: 'Replace string concatenation with template literals',
      pattern: /(['"])(.*?)\1\s*\+\s*(['"])(.*?)\3/g,
      replacement: '`$2$4`',
      category: 'readability',
      severity: 'low',
      language: ['javascript', 'typescript'],
      enabled: true
    });

    this.optimizationRules.set('js_strict_equality', {
      id: 'js_strict_equality',
      name: 'Use strict equality operators',
      description: 'Replace == and != with === and !== for type safety',
      pattern: /([^=!])([=!])=([^=])/g,
      replacement: '$1$2==$3',
      category: 'best-practices',
      severity: 'high',
      language: ['javascript', 'typescript'],
      enabled: true
    });

    // Performance optimization rules
    this.optimizationRules.set('js_array_methods', {
      id: 'js_array_methods',
      name: 'Use efficient array methods',
      description: 'Replace for loops with more efficient array methods',
      pattern: /for\s*\(\s*let\s+\w+\s*=\s*0;\s*\w+\s*<\s*\w+\.length;\s*\w+\+\+\s*\)/g,
      replacement: (match: string) => {
        // This would need more sophisticated analysis
        return match; // Placeholder
      },
      category: 'performance',
      severity: 'medium',
      language: ['javascript', 'typescript'],
      enabled: true
    });

    // Security optimization rules
    this.optimizationRules.set('js_eval_usage', {
      id: 'js_eval_usage',
      name: 'Avoid eval() usage',
      description: 'Replace eval() with safer alternatives',
      pattern: /\beval\s*\(/g,
      replacement: '// SECURITY: Consider using JSON.parse() or other safe alternatives instead of eval(',
      category: 'security',
      severity: 'critical',
      language: ['javascript', 'typescript'],
      enabled: true
    });

    // Add more rules for other languages...
    this.addPythonOptimizationRules();
    this.addJavaOptimizationRules();
    this.addGeneralOptimizationRules();
  }

  private addPythonOptimizationRules(): void {
    this.optimizationRules.set('py_list_comprehension', {
      id: 'py_list_comprehension',
      name: 'Use list comprehensions',
      description: 'Replace for loops with list comprehensions for better performance',
      pattern: /for\s+\w+\s+in\s+\w+:\s*\n\s*\w+\.append\(/g,
      replacement: (match: string) => {
        // This would need more sophisticated analysis
        return match; // Placeholder
      },
      category: 'performance',
      severity: 'medium',
      language: ['python'],
      enabled: true
    });

    this.optimizationRules.set('py_string_formatting', {
      id: 'py_string_formatting',
      name: 'Use f-strings for string formatting',
      description: 'Replace % formatting and .format() with f-strings',
      pattern: /(['"])(.*)%\s*\(/g,
      replacement: 'f$1$2{',
      category: 'readability',
      severity: 'low',
      language: ['python'],
      enabled: true
    });
  }

  private addJavaOptimizationRules(): void {
    this.optimizationRules.set('java_string_builder', {
      id: 'java_string_builder',
      name: 'Use StringBuilder for string concatenation',
      description: 'Replace string concatenation in loops with StringBuilder',
      pattern: /String\s+\w+\s*=\s*['"]['"]\s*;\s*for\s*\([^)]*\)\s*\{[^}]*\w+\s*\+=\s*[^}]*\}/g,
      replacement: (match: string) => {
        // This would need more sophisticated analysis
        return match; // Placeholder
      },
      category: 'performance',
      severity: 'high',
      language: ['java'],
      enabled: true
    });
  }

  private addGeneralOptimizationRules(): void {
    this.optimizationRules.set('remove_console_logs', {
      id: 'remove_console_logs',
      name: 'Remove console.log statements',
      description: 'Remove debugging console.log statements from production code',
      pattern: /console\.log\([^)]*\);\s*\n?/g,
      replacement: '',
      category: 'best-practices',
      severity: 'low',
      language: ['javascript', 'typescript'],
      enabled: true
    });

    this.optimizationRules.set('remove_empty_lines', {
      id: 'remove_empty_lines',
      name: 'Remove excessive empty lines',
      description: 'Remove multiple consecutive empty lines',
      pattern: /\n\s*\n\s*\n+/g,
      replacement: '\n\n',
      category: 'readability',
      severity: 'low',
      language: ['javascript', 'typescript', 'python', 'java', 'cpp', 'csharp'],
      enabled: true
    });
  }

  private async analyzeCode(targetPath: string, options: any): Promise<ToolResult> {
    try {
      const files = await this.getFilesToProcess(targetPath, options);
      const analysis = {
        totalFiles: files.length,
        languages: new Set<string>(),
        issues: [] as OptimizationSuggestion[],
        metrics: {} as Record<string, CodeMetrics>
      };

      for (const file of files) {
        const language = this.detectLanguage(file, options.language);
        analysis.languages.add(language);

        const fileIssues = await this.analyzeFile(file, language);
        analysis.issues.push(...fileIssues);

        const metrics = await this.calculateFileMetrics(file, language);
        analysis.metrics[file] = metrics;
      }

      const output = this.formatAnalysisResults(analysis, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'analyze',
          analysis,
          filesProcessed: files.length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Code analysis failed: ${error.message}`
      };
    }
  }

  private async optimizeCode(targetPath: string, options: any): Promise<ToolResult> {
    try {
      const files = await this.getFilesToProcess(targetPath, options);
      const optimizations = [];

      for (const file of files) {
        const language = this.detectLanguage(file, options.language);
        const suggestions = await this.analyzeFile(file, language, options);

        if (options.auto_fix) {
          const applied = await this.applyOptimizations(file, suggestions, options.backup);
          optimizations.push({
            file,
            applied: applied.length,
            suggestions: suggestions.length
          });
        } else {
          optimizations.push({
            file,
            suggestions: suggestions.length,
            applied: 0
          });
        }
      }

      const output = this.formatOptimizationResults(optimizations, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'optimize',
          optimizations,
          filesProcessed: files.length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Code optimization failed: ${error.message}`
      };
    }
  }

  private async refactorCode(targetPath: string, options: any): Promise<ToolResult> {
    // Placeholder for advanced refactoring
    return {
      success: true,
      output: 'Advanced refactoring features coming soon',
      metadata: {
        action: 'refactor',
        path: targetPath
      }
    };
  }

  private async calculateMetrics(targetPath: string, options: any): Promise<ToolResult> {
    try {
      const files = await this.getFilesToProcess(targetPath, options);
      const allMetrics: Record<string, CodeMetrics> = {};

      for (const file of files) {
        const language = this.detectLanguage(file, options.language);
        const metrics = await this.calculateFileMetrics(file, language);
        allMetrics[file] = metrics;
      }

      const summary = this.calculateMetricsSummary(allMetrics);
      const output = this.formatMetricsResults(allMetrics, summary, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'metrics',
          metrics: allMetrics,
          summary,
          filesProcessed: files.length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Metrics calculation failed: ${error.message}`
      };
    }
  }

  private async generateSuggestions(targetPath: string, options: any): Promise<ToolResult> {
    try {
      const files = await this.getFilesToProcess(targetPath, options);
      const allSuggestions: OptimizationSuggestion[] = [];

      for (const file of files) {
        const language = this.detectLanguage(file, options.language);
        const suggestions = await this.analyzeFile(file, language, options);
        allSuggestions.push(...suggestions);
      }

      // Filter by categories and severity
      const filteredSuggestions = this.filterSuggestions(allSuggestions, options);
      const output = this.formatSuggestions(filteredSuggestions, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'suggestions',
          suggestions: filteredSuggestions,
          totalSuggestions: allSuggestions.length,
          filesProcessed: files.length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Suggestion generation failed: ${error.message}`
      };
    }
  }

  private async autoFixIssues(targetPath: string, options: any): Promise<ToolResult> {
    try {
      const files = await this.getFilesToProcess(targetPath, options);
      const fixResults = [];

      for (const file of files) {
        const language = this.detectLanguage(file, options.language);
        const suggestions = await this.analyzeFile(file, language, options);
        const autoFixableSuggestions = suggestions.filter(s => s.autoFixable);

        if (autoFixableSuggestions.length > 0) {
          const applied = await this.applyOptimizations(file, autoFixableSuggestions, options.backup);
          fixResults.push({
            file,
            applied: applied.length,
            total: autoFixableSuggestions.length
          });
        }
      }

      const output = this.formatAutoFixResults(fixResults, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'auto_fix',
          fixResults,
          filesProcessed: files.length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Auto-fix failed: ${error.message}`
      };
    }
  }

  private async benchmarkCode(targetPath: string, options: any): Promise<ToolResult> {
    // Placeholder for code benchmarking
    return {
      success: true,
      output: 'Code benchmarking features coming soon',
      metadata: {
        action: 'benchmark',
        path: targetPath
      }
    };
  }

  private async compareOptimizations(targetPath: string, options: any): Promise<ToolResult> {
    // Placeholder for optimization comparison
    return {
      success: true,
      output: 'Optimization comparison features coming soon',
      metadata: {
        action: 'compare',
        path: targetPath
      }
    };
  }

  private async getFilesToProcess(targetPath: string, options: any): Promise<string[]> {
    try {
      const stats = await fs.promises.stat(targetPath);

      if (stats.isFile()) {
        return [targetPath];
      }

      const patterns = ['**/*.js', '**/*.ts', '**/*.jsx', '**/*.tsx', '**/*.py', '**/*.java', '**/*.cpp', '**/*.c', '**/*.h', '**/*.cs'];
      const files = await glob(patterns, {
        cwd: targetPath,
        ignore: options.exclude_patterns,
        absolute: true,
        nodir: true
      });

      return files;
    } catch (error) {
      throw new Error(`Failed to get files to process: ${error}`);
    }
  }

  private detectLanguage(filePath: string, languageHint: string): string {
    if (languageHint !== 'auto') {
      return languageHint;
    }

    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'cpp',
      '.h': 'cpp',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust'
    };

    return languageMap[ext] || 'unknown';
  }

  private async analyzeFile(filePath: string, language: string, options?: any): Promise<OptimizationSuggestion[]> {
    try {
      const content = await readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      const suggestions: OptimizationSuggestion[] = [];

      // Get applicable rules for this language
      const applicableRules = Array.from(this.optimizationRules.values())
        .filter(rule => rule.enabled && rule.language.includes(language));

      for (const rule of applicableRules) {
        // Skip if specific rules are requested and this isn't one of them
        if (options?.rules && !options.rules.includes(rule.id)) {
          continue;
        }

        // Skip if specific categories are requested and this isn't one of them
        if (options?.categories && !options.categories.includes(rule.category)) {
          continue;
        }

        // Skip if severity is below threshold
        if (options?.severity && this.getSeverityLevel(rule.severity) < this.getSeverityLevel(options.severity)) {
          continue;
        }

        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
          const line = lines[lineIndex];
          const matches = line.matchAll(new RegExp(rule.pattern.source, rule.pattern.flags));

          for (const match of matches) {
            const originalCode = match[0];
            let optimizedCode: string;

            if (typeof rule.replacement === 'function') {
              optimizedCode = rule.replacement(originalCode, ...match.slice(1));
            } else {
              optimizedCode = originalCode.replace(rule.pattern, rule.replacement);
            }

            suggestions.push({
              file: filePath,
              line: lineIndex + 1,
              column: match.index || 0,
              rule,
              originalCode,
              optimizedCode,
              impact: this.calculateImpact(rule),
              confidence: this.calculateConfidence(rule, originalCode),
              autoFixable: this.isAutoFixable(rule, originalCode)
            });
          }
        }
      }

      return suggestions;
    } catch (error) {
      return [];
    }
  }

  private async calculateFileMetrics(filePath: string, language: string): Promise<CodeMetrics> {
    try {
      const content = await readFile(filePath, 'utf-8');
      const lines = content.split('\n');

      // Basic metrics calculation (simplified)
      const metrics: CodeMetrics = {
        file: filePath,
        lines: lines.length,
        complexity: {
          cyclomatic: this.calculateCyclomaticComplexity(content, language),
          cognitive: this.calculateCognitiveComplexity(content, language),
          halstead: this.calculateHalsteadMetrics(content, language)
        },
        maintainability: {
          index: this.calculateMaintainabilityIndex(content, language),
          techDebt: this.calculateTechnicalDebt(content, language)
        },
        duplicates: {
          blocks: 0, // Would need more sophisticated analysis
          lines: 0,
          percentage: 0
        },
        issues: {
          bugs: 0, // Would need static analysis
          vulnerabilities: 0,
          codeSmells: 0,
          hotspots: 0
        }
      };

      this.codeMetricsCache.set(filePath, metrics);
      return metrics;
    } catch (error) {
      return {
        file: filePath,
        lines: 0,
        complexity: {
          cyclomatic: 0,
          cognitive: 0,
          halstead: { vocabulary: 0, length: 0, volume: 0, difficulty: 0, effort: 0 }
        },
        maintainability: { index: 0, techDebt: '0h' },
        duplicates: { blocks: 0, lines: 0, percentage: 0 },
        issues: { bugs: 0, vulnerabilities: 0, codeSmells: 0, hotspots: 0 }
      };
    }
  }

  private calculateCyclomaticComplexity(content: string, language: string): number {
    // Simplified cyclomatic complexity calculation
    const complexityKeywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', 'try', '&&', '||', '?'];
    let complexity = 1; // Base complexity

    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  private calculateCognitiveComplexity(content: string, language: string): number {
    // Simplified cognitive complexity calculation
    return Math.round(this.calculateCyclomaticComplexity(content, language) * 0.8);
  }

  private calculateHalsteadMetrics(content: string, language: string): CodeMetrics['complexity']['halstead'] {
    // Simplified Halstead metrics
    const operators = content.match(/[+\-*/=<>!&|%^~]/g) || [];
    const operands = content.match(/\b\w+\b/g) || [];

    const uniqueOperators = new Set(operators).size;
    const uniqueOperands = new Set(operands).size;

    const vocabulary = uniqueOperators + uniqueOperands;
    const length = operators.length + operands.length;
    const volume = length * Math.log2(vocabulary || 1);
    const difficulty = (uniqueOperators / 2) * (operands.length / (uniqueOperands || 1));
    const effort = difficulty * volume;

    return {
      vocabulary,
      length,
      volume,
      difficulty,
      effort
    };
  }

  private calculateMaintainabilityIndex(content: string, language: string): number {
    // Simplified maintainability index
    const halstead = this.calculateHalsteadMetrics(content, language);
    const cyclomatic = this.calculateCyclomaticComplexity(content, language);
    const linesOfCode = content.split('\n').length;

    // Microsoft's maintainability index formula (simplified)
    const mi = Math.max(0,
      171 - 5.2 * Math.log(halstead.volume) - 0.23 * cyclomatic - 16.2 * Math.log(linesOfCode)
    );

    return Math.round(mi);
  }

  private calculateTechnicalDebt(content: string, language: string): string {
    // Simplified technical debt calculation
    const complexity = this.calculateCyclomaticComplexity(content, language);
    const debtMinutes = complexity * 5; // 5 minutes per complexity point

    if (debtMinutes < 60) {
      return `${debtMinutes}m`;
    } else {
      const hours = Math.round(debtMinutes / 60 * 10) / 10;
      return `${hours}h`;
    }
  }

  private getSeverityLevel(severity: string): number {
    const levels = { low: 1, medium: 2, high: 3, critical: 4 };
    return levels[severity as keyof typeof levels] || 1;
  }

  private calculateImpact(rule: OptimizationRule): string {
    const impacts = {
      performance: 'High performance improvement',
      security: 'Critical security enhancement',
      readability: 'Improved code readability',
      maintainability: 'Better code maintainability',
      'best-practices': 'Follows best practices'
    };
    return impacts[rule.category] || 'General improvement';
  }

  private calculateConfidence(rule: OptimizationRule, code: string): number {
    // Simplified confidence calculation
    if (rule.category === 'security') return 0.95;
    if (rule.category === 'performance') return 0.85;
    if (rule.category === 'best-practices') return 0.90;
    return 0.75;
  }

  private isAutoFixable(rule: OptimizationRule, code: string): boolean {
    // Simple rules are generally auto-fixable
    const autoFixableRules = ['js_const_let', 'js_strict_equality', 'remove_console_logs', 'remove_empty_lines'];
    return autoFixableRules.includes(rule.id);
  }

  private filterSuggestions(suggestions: OptimizationSuggestion[], options: any): OptimizationSuggestion[] {
    return suggestions.filter(suggestion => {
      if (options.categories && !options.categories.includes(suggestion.rule.category)) {
        return false;
      }
      if (options.severity && this.getSeverityLevel(suggestion.rule.severity) < this.getSeverityLevel(options.severity)) {
        return false;
      }
      return true;
    });
  }

  private async applyOptimizations(filePath: string, suggestions: OptimizationSuggestion[], createBackup: boolean): Promise<OptimizationSuggestion[]> {
    try {
      if (createBackup) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await fs.promises.copyFile(filePath, backupPath);
      }

      let content = await readFile(filePath, 'utf-8');
      const applied: OptimizationSuggestion[] = [];

      // Sort suggestions by line number (descending) to avoid offset issues
      const sortedSuggestions = suggestions
        .filter(s => s.autoFixable)
        .sort((a, b) => b.line - a.line);

      for (const suggestion of sortedSuggestions) {
        const lines = content.split('\n');
        const lineIndex = suggestion.line - 1;

        if (lineIndex >= 0 && lineIndex < lines.length) {
          const originalLine = lines[lineIndex];
          const newLine = originalLine.replace(suggestion.rule.pattern,
            typeof suggestion.rule.replacement === 'string'
              ? suggestion.rule.replacement
              : suggestion.optimizedCode
          );

          if (newLine !== originalLine) {
            lines[lineIndex] = newLine;
            content = lines.join('\n');
            applied.push(suggestion);
          }
        }
      }

      if (applied.length > 0) {
        await writeFile(filePath, content, 'utf-8');
      }

      return applied;
    } catch (error) {
      return [];
    }
  }

  private calculateMetricsSummary(allMetrics: Record<string, CodeMetrics>): any {
    const files = Object.values(allMetrics);

    return {
      totalFiles: files.length,
      totalLines: files.reduce((sum, m) => sum + m.lines, 0),
      averageComplexity: files.reduce((sum, m) => sum + m.complexity.cyclomatic, 0) / files.length,
      averageMaintainability: files.reduce((sum, m) => sum + m.maintainability.index, 0) / files.length,
      totalIssues: files.reduce((sum, m) => sum + m.issues.bugs + m.issues.vulnerabilities + m.issues.codeSmells, 0)
    };
  }

  private formatAnalysisResults(analysis: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(analysis, null, 2);
    }

    return `
🔍 Code Analysis Results
========================

Files Analyzed: ${analysis.totalFiles}
Languages: ${Array.from(analysis.languages).join(', ')}
Total Issues: ${analysis.issues.length}

Issue Breakdown:
${this.formatIssueBreakdown(analysis.issues)}

Top Issues:
${analysis.issues.slice(0, 5).map((issue: OptimizationSuggestion) =>
  `  ${issue.rule.name} (${issue.file}:${issue.line})`
).join('\n')}
`;
  }

  private formatOptimizationResults(optimizations: any[], format: string): string {
    if (format === 'json') {
      return JSON.stringify(optimizations, null, 2);
    }

    const totalSuggestions = optimizations.reduce((sum, opt) => sum + opt.suggestions, 0);
    const totalApplied = optimizations.reduce((sum, opt) => sum + opt.applied, 0);

    return `
🚀 Code Optimization Results
============================

Files Processed: ${optimizations.length}
Total Suggestions: ${totalSuggestions}
Applied Optimizations: ${totalApplied}

File Results:
${optimizations.map(opt =>
  `  ${path.basename(opt.file)}: ${opt.applied}/${opt.suggestions} optimizations`
).join('\n')}
`;
  }

  private formatMetricsResults(allMetrics: Record<string, CodeMetrics>, summary: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify({ metrics: allMetrics, summary }, null, 2);
    }

    return `
📊 Code Metrics Report
======================

Summary:
  Files: ${summary.totalFiles}
  Total Lines: ${summary.totalLines}
  Average Complexity: ${summary.averageComplexity.toFixed(1)}
  Average Maintainability: ${summary.averageMaintainability.toFixed(1)}
  Total Issues: ${summary.totalIssues}

Top Complex Files:
${Object.values(allMetrics)
  .sort((a, b) => b.complexity.cyclomatic - a.complexity.cyclomatic)
  .slice(0, 5)
  .map(m => `  ${path.basename(m.file)}: ${m.complexity.cyclomatic} complexity`)
  .join('\n')}
`;
  }

  private formatSuggestions(suggestions: OptimizationSuggestion[], format: string): string {
    if (format === 'json') {
      return JSON.stringify(suggestions, null, 2);
    }

    return `
💡 Optimization Suggestions
===========================

Total Suggestions: ${suggestions.length}

${suggestions.slice(0, 10).map(s => `
${s.rule.name}
  File: ${s.file}:${s.line}
  Category: ${s.rule.category}
  Severity: ${s.rule.severity}
  Impact: ${s.impact}
  Auto-fixable: ${s.autoFixable ? 'Yes' : 'No'}

  Original: ${s.originalCode}
  Optimized: ${s.optimizedCode}
`).join('\n')}

${suggestions.length > 10 ? `... and ${suggestions.length - 10} more suggestions` : ''}
`;
  }

  private formatAutoFixResults(fixResults: any[], format: string): string {
    if (format === 'json') {
      return JSON.stringify(fixResults, null, 2);
    }

    const totalApplied = fixResults.reduce((sum, result) => sum + result.applied, 0);

    return `
🔧 Auto-Fix Results
===================

Files Modified: ${fixResults.length}
Total Fixes Applied: ${totalApplied}

Results:
${fixResults.map(result =>
  `  ${path.basename(result.file)}: ${result.applied}/${result.total} fixes applied`
).join('\n')}
`;
  }

  private formatIssueBreakdown(issues: OptimizationSuggestion[]): string {
    const breakdown = issues.reduce((acc, issue) => {
      acc[issue.rule.category] = (acc[issue.rule.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(breakdown)
      .map(([category, count]) => `  ${category}: ${count}`)
      .join('\n');
  }
}
