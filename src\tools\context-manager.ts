import * as fs from 'fs';
import * as path from 'path';
import { Tool, ToolResult, ContextFile, Message } from '../types';
import { ContextManager } from '../context/manager';

export class ContextManagerTool implements Tool {
  name = 'context_manager';
  description = 'Advanced context management with smart file retrieval, real-time monitoring, and intelligent filtering';

  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['smart_retrieve', 'add_files', 'remove_files', 'list_context', 'monitor_start', 'monitor_stop', 'optimize', 'analyze_relevance', 'get_summary', 'search_files'],
        description: 'Context management action to perform'
      },
      query: {
        type: 'string',
        description: 'User query for smart context retrieval'
      },
      paths: {
        type: 'array',
        items: { type: 'string' },
        description: 'File or directory paths to add/remove'
      },
      conversation_history: {
        type: 'array',
        items: { type: 'object' },
        description: 'Recent conversation messages for context analysis'
      },
      max_tokens: {
        type: 'number',
        description: 'Maximum tokens for context retrieval (default: auto)'
      },
      pattern: {
        type: 'string',
        description: 'Search pattern for files (regex supported)'
      },
      language: {
        type: 'string',
        description: 'Filter by programming language'
      },
      recursive: {
        type: 'boolean',
        description: 'Process directories recursively (default: true)'
      },
      format: {
        type: 'string',
        enum: ['summary', 'detailed', 'json', 'list'],
        description: 'Output format (default: summary)'
      }
    },
    required: ['action']
  };

  private contextManager: ContextManager;

  constructor(contextManager: ContextManager) {
    this.contextManager = contextManager;
  }

  async execute(args: {
    action: string;
    query?: string;
    paths?: string[];
    conversation_history?: Message[];
    max_tokens?: number;
    pattern?: string;
    language?: string;
    recursive?: boolean;
    format?: string;
  }): Promise<ToolResult> {
    try {
      const {
        action,
        query,
        paths = [],
        conversation_history = [],
        max_tokens,
        pattern,
        language,
        recursive = true,
        format = 'summary'
      } = args;

      switch (action) {
        case 'smart_retrieve':
          return await this.smartRetrieve(query || '', conversation_history, max_tokens, format);

        case 'add_files':
          return await this.addFiles(paths, recursive, format);

        case 'remove_files':
          return await this.removeFiles(paths, format);

        case 'list_context':
          return await this.listContext(language, pattern, format);

        case 'monitor_start':
          return await this.startMonitoring(paths, format);

        case 'monitor_stop':
          return await this.stopMonitoring(paths, format);

        case 'optimize':
          return await this.optimizeContext(format);

        case 'analyze_relevance':
          return await this.analyzeRelevance(query || '', conversation_history, format);

        case 'get_summary':
          return await this.getContextSummary(format);

        case 'search_files':
          return await this.searchFiles(pattern || '', language, format);

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Context management operation failed'
      };
    }
  }

  private async smartRetrieve(
    query: string,
    conversationHistory: Message[],
    maxTokens?: number,
    format: string = 'summary'
  ): Promise<ToolResult> {
    if (!query.trim()) {
      throw new Error('Query is required for smart retrieval');
    }

    const result = await this.contextManager.getRelevantContext(
      query,
      conversationHistory,
      maxTokens
    );

    const output = this.formatSmartRetrievalResult(result, format);

    return {
      success: true,
      output,
      metadata: {
        action: 'smart_retrieve',
        query,
        filesSelected: result.files.length,
        totalTokens: result.totalTokens,
        reasoning: result.reasoning
      }
    };
  }

  private async addFiles(paths: string[], recursive: boolean, format: string): Promise<ToolResult> {
    if (paths.length === 0) {
      throw new Error('At least one path is required');
    }

    const results = [];
    let totalAdded = 0;

    for (const filePath of paths) {
      try {
        const resolvedPath = path.resolve(filePath);

        if (!fs.existsSync(resolvedPath)) {
          results.push({ path: filePath, status: 'not_found', error: 'File or directory not found' });
          continue;
        }

        const stats = fs.statSync(resolvedPath);

        if (stats.isDirectory() && recursive) {
          await this.contextManager.addFile(resolvedPath);
          const addedCount = this.countFilesInDirectory(resolvedPath);
          results.push({ path: filePath, status: 'added', filesAdded: addedCount });
          totalAdded += addedCount;
        } else if (stats.isFile()) {
          await this.contextManager.addFile(resolvedPath);
          results.push({ path: filePath, status: 'added', filesAdded: 1 });
          totalAdded += 1;
        } else {
          results.push({ path: filePath, status: 'skipped', error: 'Directory (use recursive=true)' });
        }
      } catch (error: any) {
        results.push({ path: filePath, status: 'error', error: error.message });
      }
    }

    const output = this.formatAddFilesResult(results, totalAdded, format);

    return {
      success: true,
      output,
      metadata: {
        action: 'add_files',
        results,
        totalAdded
      }
    };
  }

  private async removeFiles(paths: string[], format: string): Promise<ToolResult> {
    if (paths.length === 0) {
      throw new Error('At least one path is required');
    }

    const results = [];
    let totalRemoved = 0;

    for (const filePath of paths) {
      try {
        const resolvedPath = path.resolve(filePath);
        this.contextManager.removeFile(resolvedPath);
        results.push({ path: filePath, status: 'removed' });
        totalRemoved++;
      } catch (error: any) {
        results.push({ path: filePath, status: 'error', error: error.message });
      }
    }

    const output = this.formatRemoveFilesResult(results, totalRemoved, format);

    return {
      success: true,
      output,
      metadata: {
        action: 'remove_files',
        results,
        totalRemoved
      }
    };
  }

  private async listContext(language?: string, pattern?: string, format: string = 'summary'): Promise<ToolResult> {
    let files = this.contextManager.getContext();

    // Filter by language if specified
    if (language) {
      files = files.filter(file => file.language === language);
    }

    // Filter by pattern if specified
    if (pattern) {
      const regex = new RegExp(pattern, 'i');
      files = files.filter(file => regex.test(file.path) || regex.test(file.content));
    }

    const output = this.formatContextList(files, format);

    return {
      success: true,
      output,
      metadata: {
        action: 'list_context',
        totalFiles: files.length,
        totalTokens: this.contextManager.getTotalTokens(),
        filters: { language, pattern }
      }
    };
  }

  private async startMonitoring(paths: string[], format: string): Promise<ToolResult> {
    if (paths.length === 0) {
      throw new Error('At least one directory path is required for monitoring');
    }

    const results = [];

    for (const dirPath of paths) {
      try {
        const resolvedPath = path.resolve(dirPath);

        if (!fs.existsSync(resolvedPath)) {
          results.push({ path: dirPath, status: 'not_found' });
          continue;
        }

        const stats = fs.statSync(resolvedPath);

        if (!stats.isDirectory()) {
          results.push({ path: dirPath, status: 'not_directory' });
          continue;
        }

        this.contextManager.startMonitoring(resolvedPath);
        results.push({ path: dirPath, status: 'monitoring' });
      } catch (error: any) {
        results.push({ path: dirPath, status: 'error', error: error.message });
      }
    }

    const output = this.formatMonitoringResult(results, 'started', format);

    return {
      success: true,
      output,
      metadata: {
        action: 'monitor_start',
        results
      }
    };
  }

  private async stopMonitoring(paths: string[], format: string): Promise<ToolResult> {
    const results = [];

    if (paths.length === 0) {
      // Stop all monitoring
      this.contextManager.stopMonitoring();
      results.push({ path: 'all', status: 'stopped' });
    } else {
      for (const dirPath of paths) {
        try {
          const resolvedPath = path.resolve(dirPath);
          this.contextManager.stopMonitoring(resolvedPath);
          results.push({ path: dirPath, status: 'stopped' });
        } catch (error: any) {
          results.push({ path: dirPath, status: 'error', error: error.message });
        }
      }
    }

    const output = this.formatMonitoringResult(results, 'stopped', format);

    return {
      success: true,
      output,
      metadata: {
        action: 'monitor_stop',
        results
      }
    };
  }

  private async optimizeContext(format: string): Promise<ToolResult> {
    const beforeTokens = this.contextManager.getTotalTokens();
    const beforeFiles = this.contextManager.getContext().length;

    this.contextManager.optimize();

    const afterTokens = this.contextManager.getTotalTokens();
    const afterFiles = this.contextManager.getContext().length;

    const tokensSaved = beforeTokens - afterTokens;
    const filesRemoved = beforeFiles - afterFiles;

    const output = this.formatOptimizationResult({
      beforeTokens,
      afterTokens,
      tokensSaved,
      beforeFiles,
      afterFiles,
      filesRemoved
    }, format);

    return {
      success: true,
      output,
      metadata: {
        action: 'optimize',
        beforeTokens,
        afterTokens,
        tokensSaved,
        beforeFiles,
        afterFiles,
        filesRemoved
      }
    };
  }

  private async analyzeRelevance(
    query: string,
    conversationHistory: Message[],
    format: string
  ): Promise<ToolResult> {
    if (!query.trim()) {
      throw new Error('Query is required for relevance analysis');
    }

    const result = await this.contextManager.getRelevantContext(
      query,
      conversationHistory
    );

    // Get all files with their relevance scores for analysis
    const allFiles = this.contextManager.getContext();
    const analysis = {
      query,
      totalFiles: allFiles.length,
      relevantFiles: result.files.length,
      totalTokens: result.totalTokens,
      reasoning: result.reasoning,
      topFiles: result.files.slice(0, 5).map(file => ({
        path: file.path,
        language: file.language,
        size: file.size,
        tokens: Math.ceil(file.content.length * 0.25)
      }))
    };

    const output = this.formatRelevanceAnalysis(analysis, format);

    return {
      success: true,
      output,
      metadata: {
        action: 'analyze_relevance',
        analysis
      }
    };
  }

  private async getContextSummary(format: string): Promise<ToolResult> {
    const summary = this.contextManager.getContextSummary();

    const output = format === 'json' ? summary : this.formatSummary(JSON.parse(summary), format);

    return {
      success: true,
      output,
      metadata: {
        action: 'get_summary',
        summary: JSON.parse(summary)
      }
    };
  }

  private async searchFiles(pattern: string, language?: string, format: string = 'summary'): Promise<ToolResult> {
    if (!pattern.trim()) {
      throw new Error('Search pattern is required');
    }

    const files = this.contextManager.getFilesByPattern(pattern);
    let filteredFiles = files;

    if (language) {
      filteredFiles = files.filter(file => file.language === language);
    }

    const output = this.formatSearchResults(filteredFiles, pattern, language, format);

    return {
      success: true,
      output,
      metadata: {
        action: 'search_files',
        pattern,
        language,
        totalMatches: filteredFiles.length,
        totalFiles: files.length
      }
    };
  }

  // Helper methods for formatting outputs
  private countFilesInDirectory(dirPath: string): number {
    // This is a simplified count - in practice, you'd want to use the same logic as addFile
    try {
      const files = fs.readdirSync(dirPath, { recursive: true });
      return files.filter(file => typeof file === 'string' && !file.includes('node_modules')).length;
    } catch {
      return 0;
    }
  }

  private formatSmartRetrievalResult(result: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(result, null, 2);
    }

    return `
🧠 Smart Context Retrieval Results
==================================

Selected Files: ${result.files.length}
Total Tokens: ${result.totalTokens.toLocaleString()}

Reasoning: ${result.reasoning}

Selected Files:
${result.files.map((file: ContextFile) =>
  `  📄 ${path.basename(file.path)} (${file.language || 'unknown'}) - ${Math.ceil(file.content.length * 0.25)} tokens`
).join('\n')}
`;
  }

  private formatAddFilesResult(results: any[], totalAdded: number, format: string): string {
    if (format === 'json') {
      return JSON.stringify({ results, totalAdded }, null, 2);
    }

    const successful = results.filter(r => r.status === 'added').length;
    const failed = results.filter(r => r.status === 'error').length;

    return `
📁 Add Files Results
===================

Total Files Added: ${totalAdded}
Successful Operations: ${successful}
Failed Operations: ${failed}

Details:
${results.map(r =>
  `  ${r.status === 'added' ? '✅' : r.status === 'error' ? '❌' : '⚠️'} ${r.path}${r.error ? ` - ${r.error}` : ''}`
).join('\n')}
`;
  }

  private formatRemoveFilesResult(results: any[], totalRemoved: number, format: string): string {
    if (format === 'json') {
      return JSON.stringify({ results, totalRemoved }, null, 2);
    }

    return `
🗑️  Remove Files Results
========================

Total Files Removed: ${totalRemoved}

Details:
${results.map(r =>
  `  ${r.status === 'removed' ? '✅' : '❌'} ${r.path}${r.error ? ` - ${r.error}` : ''}`
).join('\n')}
`;
  }

  private formatContextList(files: ContextFile[], format: string): string {
    if (format === 'json') {
      return JSON.stringify(files, null, 2);
    }

    if (format === 'list') {
      return files.map(f => f.path).join('\n');
    }

    const totalTokens = files.reduce((sum, f) => sum + Math.ceil(f.content.length * 0.25), 0);
    const languages = [...new Set(files.map(f => f.language).filter(Boolean))];

    return `
📋 Context Files
===============

Total Files: ${files.length}
Total Tokens: ${totalTokens.toLocaleString()}
Languages: ${languages.join(', ')}

Files:
${files.map(file =>
  `  📄 ${path.basename(file.path)} (${file.language || 'unknown'}) - ${Math.ceil(file.content.length * 0.25)} tokens`
).join('\n')}
`;
  }

  private formatMonitoringResult(results: any[], action: string, format: string): string {
    if (format === 'json') {
      return JSON.stringify({ action, results }, null, 2);
    }

    const successful = results.filter(r => r.status === 'monitoring' || r.status === 'stopped').length;

    return `
👁️  File Monitoring ${action.charAt(0).toUpperCase() + action.slice(1)}
${'='.repeat(20 + action.length)}

Successful Operations: ${successful}

Details:
${results.map(r =>
  `  ${r.status === 'monitoring' || r.status === 'stopped' ? '✅' : '❌'} ${r.path}${r.error ? ` - ${r.error}` : ''}`
).join('\n')}
`;
  }

  private formatOptimizationResult(data: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    }

    const efficiency = data.beforeTokens > 0 ? ((data.tokensSaved / data.beforeTokens) * 100).toFixed(1) : '0';

    return `
⚡ Context Optimization Results
==============================

Before: ${data.beforeFiles} files, ${data.beforeTokens.toLocaleString()} tokens
After:  ${data.afterFiles} files, ${data.afterTokens.toLocaleString()} tokens

Savings: ${data.filesRemoved} files, ${data.tokensSaved.toLocaleString()} tokens (${efficiency}% reduction)
`;
  }

  private formatRelevanceAnalysis(analysis: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(analysis, null, 2);
    }

    return `
🎯 Relevance Analysis
====================

Query: "${analysis.query}"
Total Files: ${analysis.totalFiles}
Relevant Files: ${analysis.relevantFiles}
Total Tokens: ${analysis.totalTokens.toLocaleString()}

Reasoning: ${analysis.reasoning}

Top Relevant Files:
${analysis.topFiles.map((file: any) =>
  `  📄 ${path.basename(file.path)} (${file.language}) - ${file.tokens} tokens`
).join('\n')}
`;
  }

  private formatSummary(summary: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(summary, null, 2);
    }

    return `
📊 Context Summary
=================

Total Files: ${summary.totalFiles}
Total Tokens: ${summary.totalTokens.toLocaleString()}

Languages:
${Object.entries(summary.languages).map(([lang, count]) =>
  `  ${lang}: ${count} files`
).join('\n')}

Recent Files:
${summary.recentFiles.map((file: any) =>
  `  📄 ${path.basename(file.path)} (${file.language})`
).join('\n')}
`;
  }

  private formatSearchResults(files: ContextFile[], pattern: string, language: string | undefined, format: string): string {
    if (format === 'json') {
      return JSON.stringify({ pattern, language, files }, null, 2);
    }

    return `
🔍 Search Results
================

Pattern: "${pattern}"
${language ? `Language: ${language}` : ''}
Matches: ${files.length}

Files:
${files.map(file =>
  `  📄 ${path.basename(file.path)} (${file.language || 'unknown'})`
).join('\n')}
`;
  }
}
