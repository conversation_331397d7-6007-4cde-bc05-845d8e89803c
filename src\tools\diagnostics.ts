import { Tool, ToolResult } from '../types';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

export class DiagnosticsTool implements Tool {
  name = 'diagnostics';
  description = 'Advanced system diagnostics for providers, connections, and system health monitoring';
  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['full', 'providers', 'network', 'system', 'api_keys', 'models', 'health'],
        description: 'Type of diagnostic to run'
      },
      provider: {
        type: 'string',
        enum: ['openai', 'anthropic', 'deepseek', 'ollama'],
        description: 'Specific provider to test (optional)'
      },
      detailed: {
        type: 'boolean',
        description: 'Include detailed diagnostic information',
        default: false
      }
    },
    required: ['action']
  };

  async execute(args: {
    action: string;
    provider?: string;
    detailed?: boolean;
  }): Promise<ToolResult> {
    try {
      const { action, provider, detailed = false } = args;

      switch (action) {
        case 'full':
          return await this.runFullDiagnostics(detailed);
        case 'providers':
          return await this.testProviders(provider, detailed);
        case 'network':
          return await this.testNetworkConnectivity(detailed);
        case 'system':
          return await this.getSystemInfo(detailed);
        case 'api_keys':
          return await this.checkApiKeys(detailed);
        case 'models':
          return await this.checkModels(provider, detailed);
        case 'health':
          return await this.healthCheck(detailed);
        default:
          throw new Error(`Unknown diagnostic action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Diagnostic operation failed',
        metadata: {
          action: args.action,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  private async runFullDiagnostics(detailed: boolean): Promise<ToolResult> {
    const results: any = {
      timestamp: new Date().toISOString(),
      system: await this.getSystemInfo(false),
      network: await this.testNetworkConnectivity(false),
      apiKeys: await this.checkApiKeys(false),
      providers: await this.testProviders(undefined, false),
      models: await this.checkModels(undefined, false),
      health: await this.healthCheck(false)
    };

    let output = '🔍 COMPREHENSIVE SYSTEM DIAGNOSTICS\n';
    output += '═'.repeat(50) + '\n\n';

    // System Information
    output += '💻 SYSTEM INFORMATION:\n';
    if (results.system.success) {
      const sysData = JSON.parse(results.system.output);
      output += `  Platform: ${sysData.platform}\n`;
      output += `  Architecture: ${sysData.arch}\n`;
      output += `  Node.js: ${sysData.nodeVersion}\n`;
      output += `  Memory: ${Math.round(sysData.totalMemory / 1024 / 1024 / 1024)}GB total, ${Math.round(sysData.freeMemory / 1024 / 1024 / 1024)}GB free\n`;
    }
    output += '\n';

    // Network Connectivity
    output += '🌐 NETWORK CONNECTIVITY:\n';
    if (results.network.success) {
      const netData = JSON.parse(results.network.output);
      output += `  Internet: ${netData.internet ? '✅ Connected' : '❌ Failed'}\n`;
      output += `  DNS Resolution: ${netData.dns ? '✅ Working' : '❌ Failed'}\n`;
      if (netData.latency) {
        output += `  Latency: ${netData.latency}ms\n`;
      }
    }
    output += '\n';

    // API Keys Status
    output += '🔑 API KEYS:\n';
    if (results.apiKeys.success) {
      const keyData = JSON.parse(results.apiKeys.output);
      for (const [key, status] of Object.entries(keyData)) {
        const icon = status ? '✅' : '❌';
        output += `  ${key}: ${icon}\n`;
      }
    }
    output += '\n';

    // Provider Status
    output += '🤖 PROVIDERS:\n';
    if (results.providers.success) {
      const providerData = JSON.parse(results.providers.output);
      for (const [provider, info] of Object.entries(providerData as any)) {
        const providerInfo = info as { working: boolean; models?: string[]; error?: string };
        const status = providerInfo.working ? '✅ Working' : '❌ Failed';
        output += `  ${provider}: ${status}\n`;
        if (detailed && providerInfo.models) {
          output += `    Models: ${providerInfo.models.join(', ')}\n`;
        }
      }
    }
    output += '\n';

    // Health Summary
    output += '📊 HEALTH SUMMARY:\n';
    if (results.health.success) {
      const healthData = JSON.parse(results.health.output);
      output += `  Overall Status: ${healthData.overall}\n`;
      output += `  Working Providers: ${healthData.workingProviders}\n`;
      output += `  Available Models: ${healthData.availableModels}\n`;
      if (healthData.recommendations && healthData.recommendations.length > 0) {
        output += '\n💡 RECOMMENDATIONS:\n';
        healthData.recommendations.forEach((rec: string, i: number) => {
          output += `  ${i + 1}. ${rec}\n`;
        });
      }
    }

    return {
      success: true,
      output,
      metadata: {
        action: 'full',
        timestamp: new Date().toISOString(),
        detailed,
        results
      }
    };
  }

  private async testProviders(provider?: string, detailed?: boolean): Promise<ToolResult> {
    const results: any = {};
    const providers = provider ? [provider] : ['openai', 'anthropic', 'deepseek', 'ollama'];

    for (const providerName of providers) {
      try {
        results[providerName] = await this.testSingleProvider(providerName, detailed);
      } catch (error) {
        results[providerName] = {
          working: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          models: []
        };
      }
    }

    let output = '🤖 PROVIDER DIAGNOSTICS\n';
    output += '═'.repeat(30) + '\n\n';

    for (const [name, info] of Object.entries(results)) {
      const status = (info as any).working ? '✅ Working' : '❌ Failed';
      output += `${name}: ${status}\n`;

      if (detailed) {
        if ((info as any).models && (info as any).models.length > 0) {
          output += `  Models: ${(info as any).models.join(', ')}\n`;
        }
        if ((info as any).error) {
          output += `  Error: ${(info as any).error}\n`;
        }
        if ((info as any).latency) {
          output += `  Response Time: ${(info as any).latency}ms\n`;
        }
      }
      output += '\n';
    }

    return {
      success: true,
      output: JSON.stringify(results),
      metadata: {
        action: 'providers',
        provider,
        detailed,
        timestamp: new Date().toISOString()
      }
    };
  }

  private async testSingleProvider(provider: string, detailed?: boolean): Promise<any> {
    const startTime = Date.now();

    switch (provider) {
      case 'openai':
        return await this.testOpenAI(detailed);
      case 'anthropic':
        return await this.testAnthropic(detailed);
      case 'deepseek':
        return await this.testDeepseek(detailed);
      case 'ollama':
        return await this.testOllama(detailed);
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }

  private async testOpenAI(detailed?: boolean): Promise<any> {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey || apiKey === 'your_api_key_here') {
      return { working: false, error: 'API key not configured', models: [] };
    }

    try {
      const startTime = Date.now();
      const response = await axios.get('https://api.openai.com/v1/models', {
        headers: { 'Authorization': `Bearer ${apiKey}` },
        timeout: 10000
      });
      const latency = Date.now() - startTime;

      const models = response.data.data
        .filter((model: any) => model.id.includes('gpt'))
        .map((model: any) => model.id)
        .slice(0, 5); // Limit to 5 models

      return {
        working: true,
        models,
        latency: detailed ? latency : undefined
      };
    } catch (error: any) {
      return {
        working: false,
        error: error.response?.data?.error?.message || error.message,
        models: []
      };
    }
  }

  private async testAnthropic(detailed?: boolean): Promise<any> {
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey || apiKey === 'your_api_key_here') {
      return { working: false, error: 'API key not configured', models: [] };
    }

    try {
      const startTime = Date.now();
      // Anthropic doesn't have a models endpoint, so we test with a simple request
      const response = await axios.post('https://api.anthropic.com/v1/messages', {
        model: 'claude-3-haiku-20240307',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'test' }]
      }, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        timeout: 10000
      });
      const latency = Date.now() - startTime;

      return {
        working: true,
        models: ['claude-3-5-sonnet-20241022', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
        latency: detailed ? latency : undefined
      };
    } catch (error: any) {
      return {
        working: false,
        error: error.response?.data?.error?.message || error.message,
        models: []
      };
    }
  }

  private async testDeepseek(detailed?: boolean): Promise<any> {
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey || apiKey === 'your_api_key_here') {
      return { working: false, error: 'API key not configured', models: [] };
    }

    try {
      const startTime = Date.now();
      const baseURL = process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1';
      const response = await axios.get(`${baseURL}/models`, {
        headers: { 'Authorization': `Bearer ${apiKey}` },
        timeout: 10000
      });
      const latency = Date.now() - startTime;

      const models = response.data.data?.map((model: any) => model.id) || ['deepseek-chat', 'deepseek-coder'];

      return {
        working: true,
        models: models.slice(0, 5),
        latency: detailed ? latency : undefined
      };
    } catch (error: any) {
      return {
        working: false,
        error: error.response?.data?.error?.message || error.message,
        models: []
      };
    }
  }

  private async testOllama(detailed?: boolean): Promise<any> {
    try {
      const startTime = Date.now();
      const baseURL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
      const response = await axios.get(`${baseURL}/api/tags`, {
        timeout: 5000
      });
      const latency = Date.now() - startTime;

      const models = response.data.models?.map((model: any) => model.name) || [];

      return {
        working: true,
        models: models.slice(0, 5),
        latency: detailed ? latency : undefined,
        serverInfo: detailed ? await this.getOllamaServerInfo() : undefined
      };
    } catch (error: any) {
      return {
        working: false,
        error: error.code === 'ECONNREFUSED' ? 'Ollama server not running' : error.message,
        models: []
      };
    }
  }

  private async getOllamaServerInfo(): Promise<any> {
    try {
      const baseURL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
      const response = await axios.get(`${baseURL}/api/version`, { timeout: 5000 });
      return response.data;
    } catch (error) {
      return null;
    }
  }

  private async testNetworkConnectivity(detailed?: boolean): Promise<ToolResult> {
    const results: any = {
      internet: false,
      dns: false,
      latency: null
    };

    // Test internet connectivity
    try {
      const startTime = Date.now();
      await axios.get('https://www.google.com', { timeout: 5000 });
      results.internet = true;
      results.latency = Date.now() - startTime;
    } catch (error) {
      results.internetError = error instanceof Error ? error.message : 'Unknown error';
    }

    // Test DNS resolution
    try {
      const dns = require('dns');
      await new Promise((resolve, reject) => {
        dns.lookup('api.openai.com', (err: any) => {
          if (err) reject(err);
          else resolve(true);
        });
      });
      results.dns = true;
    } catch (error) {
      results.dnsError = error instanceof Error ? error.message : 'Unknown error';
    }

    let output = '🌐 NETWORK CONNECTIVITY\n';
    output += '═'.repeat(25) + '\n\n';
    output += `Internet: ${results.internet ? '✅ Connected' : '❌ Failed'}\n`;
    output += `DNS Resolution: ${results.dns ? '✅ Working' : '❌ Failed'}\n`;

    if (results.latency) {
      output += `Latency: ${results.latency}ms\n`;
    }

    if (detailed) {
      if (results.internetError) {
        output += `Internet Error: ${results.internetError}\n`;
      }
      if (results.dnsError) {
        output += `DNS Error: ${results.dnsError}\n`;
      }
    }

    return {
      success: true,
      output: JSON.stringify(results),
      metadata: {
        action: 'network',
        detailed,
        timestamp: new Date().toISOString()
      }
    };
  }

  private async getSystemInfo(detailed?: boolean): Promise<ToolResult> {
    const os = require('os');

    const systemInfo: any = {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpus: os.cpus().length,
      uptime: os.uptime()
    };

    if (detailed) {
      systemInfo.hostname = os.hostname();
      systemInfo.loadAverage = os.loadavg();
      systemInfo.networkInterfaces = Object.keys(os.networkInterfaces());
    }

    let output = '💻 SYSTEM INFORMATION\n';
    output += '═'.repeat(22) + '\n\n';
    output += `Platform: ${systemInfo.platform}\n`;
    output += `Architecture: ${systemInfo.arch}\n`;
    output += `Node.js: ${systemInfo.nodeVersion}\n`;
    output += `CPUs: ${systemInfo.cpus}\n`;
    output += `Memory: ${Math.round(systemInfo.totalMemory / 1024 / 1024 / 1024)}GB total, ${Math.round(systemInfo.freeMemory / 1024 / 1024 / 1024)}GB free\n`;
    output += `Uptime: ${Math.round(systemInfo.uptime / 3600)}h ${Math.round((systemInfo.uptime % 3600) / 60)}m\n`;

    return {
      success: true,
      output: JSON.stringify(systemInfo),
      metadata: {
        action: 'system',
        detailed,
        timestamp: new Date().toISOString()
      }
    };
  }

  private async checkApiKeys(detailed?: boolean): Promise<ToolResult> {
    const keys = {
      OPENAI_API_KEY: this.isValidApiKey(process.env.OPENAI_API_KEY),
      ANTHROPIC_API_KEY: this.isValidApiKey(process.env.ANTHROPIC_API_KEY),
      DEEPSEEK_API_KEY: this.isValidApiKey(process.env.DEEPSEEK_API_KEY)
    };

    let output = '🔑 API KEYS STATUS\n';
    output += '═'.repeat(20) + '\n\n';

    for (const [key, isValid] of Object.entries(keys)) {
      const status = isValid ? '✅ Configured' : '❌ Not configured';
      output += `${key}: ${status}\n`;

      if (detailed && isValid) {
        const value = process.env[key];
        if (value) {
          output += `  Length: ${value.length} characters\n`;
          output += `  Prefix: ${value.substring(0, 8)}...\n`;
        }
      }
    }

    return {
      success: true,
      output: JSON.stringify(keys),
      metadata: {
        action: 'api_keys',
        detailed,
        timestamp: new Date().toISOString()
      }
    };
  }

  private async checkModels(provider?: string, detailed?: boolean): Promise<ToolResult> {
    const results: any = {};
    const providers = provider ? [provider] : ['openai', 'anthropic', 'deepseek', 'ollama'];

    for (const providerName of providers) {
      try {
        const providerResult = await this.testSingleProvider(providerName, detailed);
        results[providerName] = {
          available: providerResult.working,
          models: providerResult.models || [],
          count: (providerResult.models || []).length
        };
      } catch (error) {
        results[providerName] = {
          available: false,
          models: [],
          count: 0,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    let output = '🤖 MODELS AVAILABILITY\n';
    output += '═'.repeat(23) + '\n\n';

    for (const [name, info] of Object.entries(results)) {
      const modelInfo = info as { available: boolean; models: string[]; count: number; error?: string };
      const status = modelInfo.available ? '✅ Available' : '❌ Unavailable';
      output += `${name}: ${status} (${modelInfo.count} models)\n`;

      if (detailed && modelInfo.models.length > 0) {
        output += `  Models: ${modelInfo.models.join(', ')}\n`;
      }
    }

    return {
      success: true,
      output: JSON.stringify(results),
      metadata: {
        action: 'models',
        provider,
        detailed,
        timestamp: new Date().toISOString()
      }
    };
  }

  private async healthCheck(detailed?: boolean): Promise<ToolResult> {
    const providers = await this.testProviders(undefined, false);
    const network = await this.testNetworkConnectivity(false);
    const apiKeys = await this.checkApiKeys(false);

    const providerData = JSON.parse(providers.output);
    const networkData = JSON.parse(network.output);
    const keyData = JSON.parse(apiKeys.output);

    const workingProviders = Object.values(providerData).filter((p: any) => p.working).length;
    const totalProviders = Object.keys(providerData).length;
    const availableModels = Object.values(providerData).reduce((sum: number, p: any) => sum + (p.models?.length || 0), 0);
    const configuredKeys = Object.values(keyData).filter(Boolean).length;

    const health = {
      overall: workingProviders > 0 ? (workingProviders === totalProviders ? 'Excellent' : 'Good') : 'Poor',
      workingProviders: `${workingProviders}/${totalProviders}`,
      availableModels,
      configuredKeys: `${configuredKeys}/3`,
      internetConnected: networkData.internet,
      dnsWorking: networkData.dns,
      recommendations: [] as string[]
    };

    // Generate recommendations
    if (!networkData.internet) {
      health.recommendations.push('Check your internet connection');
    }
    if (!networkData.dns) {
      health.recommendations.push('Check DNS settings or firewall configuration');
    }
    if (configuredKeys === 0) {
      health.recommendations.push('Configure at least one API key in .env file');
    }
    if (workingProviders === 0) {
      health.recommendations.push('No working providers - check API keys and network connectivity');
    }
    if (workingProviders < totalProviders) {
      health.recommendations.push('Some providers are not working - run detailed diagnostics');
    }

    let output = '📊 HEALTH CHECK SUMMARY\n';
    output += '═'.repeat(25) + '\n\n';
    output += `Overall Status: ${health.overall}\n`;
    output += `Working Providers: ${health.workingProviders}\n`;
    output += `Available Models: ${health.availableModels}\n`;
    output += `Configured Keys: ${health.configuredKeys}\n`;
    output += `Internet: ${health.internetConnected ? '✅' : '❌'}\n`;
    output += `DNS: ${health.dnsWorking ? '✅' : '❌'}\n`;

    if (health.recommendations.length > 0) {
      output += '\n💡 Recommendations:\n';
      health.recommendations.forEach((rec, i) => {
        output += `  ${i + 1}. ${rec}\n`;
      });
    }

    return {
      success: true,
      output: JSON.stringify(health),
      metadata: {
        action: 'health',
        detailed,
        timestamp: new Date().toISOString()
      }
    };
  }

  private isValidApiKey(key?: string): boolean {
    return !!(key && key !== 'your_api_key_here' && key.length > 10);
  }
}
