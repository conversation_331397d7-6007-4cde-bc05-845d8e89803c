import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { Tool, ToolResult } from '../types';
import { glob } from 'glob';

const stat = promisify(fs.stat);
const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);

interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory' | 'symlink';
  size: number;
  modified: Date;
  permissions: string;
  extension?: string;
  isHidden: boolean;
  isExecutable: boolean;
  mimeType?: string;
  encoding?: string;
}

interface DirectoryTree {
  name: string;
  path: string;
  type: 'directory';
  children: (DirectoryTree | FileInfo)[];
  size: number;
  fileCount: number;
  directoryCount: number;
}

export class FileBrowserTool implements Tool {
  name = 'file_browser';
  description = 'Advanced file browser with tree view, search, filtering, and file operations';

  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['browse', 'tree', 'search', 'info', 'preview', 'compare', 'watch', 'analyze'],
        description: 'The file browser action to perform'
      },
      path: {
        type: 'string',
        description: 'Path to browse or operate on (default: current directory)'
      },
      pattern: {
        type: 'string',
        description: 'Search pattern or glob pattern for filtering'
      },
      recursive: {
        type: 'boolean',
        description: 'Perform recursive operations (default: true)'
      },
      max_depth: {
        type: 'number',
        description: 'Maximum depth for recursive operations (default: 10)'
      },
      show_hidden: {
        type: 'boolean',
        description: 'Show hidden files and directories (default: false)'
      },
      sort_by: {
        type: 'string',
        enum: ['name', 'size', 'modified', 'type', 'extension'],
        description: 'Sort files by specified criteria (default: name)'
      },
      sort_order: {
        type: 'string',
        enum: ['asc', 'desc'],
        description: 'Sort order (default: asc)'
      },
      filter_type: {
        type: 'string',
        enum: ['all', 'files', 'directories', 'images', 'documents', 'code', 'archives'],
        description: 'Filter by file type (default: all)'
      },
      format: {
        type: 'string',
        enum: ['list', 'tree', 'table', 'json', 'detailed'],
        description: 'Output format (default: list)'
      },
      preview_lines: {
        type: 'number',
        description: 'Number of lines to preview for text files (default: 20)'
      },
      compare_with: {
        type: 'string',
        description: 'Path to compare with (for compare action)'
      }
    },
    required: ['action']
  };

  async execute(args: {
    action: string;
    path?: string;
    pattern?: string;
    recursive?: boolean;
    max_depth?: number;
    show_hidden?: boolean;
    sort_by?: string;
    sort_order?: string;
    filter_type?: string;
    format?: string;
    preview_lines?: number;
    compare_with?: string;
  }): Promise<ToolResult> {
    try {
      const {
        action,
        path: targetPath = '.',
        pattern,
        recursive = true,
        max_depth = 10,
        show_hidden = false,
        sort_by = 'name',
        sort_order = 'asc',
        filter_type = 'all',
        format = 'list',
        preview_lines = 20,
        compare_with
      } = args;

      switch (action) {
        case 'browse':
          return await this.browseDirectory(targetPath, {
            show_hidden,
            sort_by,
            sort_order,
            filter_type,
            format
          });

        case 'tree':
          return await this.generateDirectoryTree(targetPath, {
            recursive,
            max_depth,
            show_hidden,
            format
          });

        case 'search':
          if (!pattern) {
            throw new Error('Pattern is required for search action');
          }
          return await this.searchFiles(targetPath, pattern, {
            recursive,
            max_depth,
            show_hidden,
            filter_type,
            format
          });

        case 'info':
          return await this.getFileInfo(targetPath, format);

        case 'preview':
          return await this.previewFile(targetPath, preview_lines, format);

        case 'compare':
          if (!compare_with) {
            throw new Error('compare_with path is required for compare action');
          }
          return await this.compareFiles(targetPath, compare_with, format);

        case 'watch':
          return await this.watchDirectory(targetPath, {
            recursive,
            show_hidden
          });

        case 'analyze':
          return await this.analyzeDirectory(targetPath, {
            recursive,
            max_depth,
            show_hidden,
            format
          });

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'File browser operation failed'
      };
    }
  }

  private async browseDirectory(dirPath: string, options: any): Promise<ToolResult> {
    try {
      if (!fs.existsSync(dirPath)) {
        throw new Error(`Directory does not exist: ${dirPath}`);
      }

      const stats = await stat(dirPath);
      if (!stats.isDirectory()) {
        throw new Error(`Path is not a directory: ${dirPath}`);
      }

      const files = await this.getDirectoryContents(dirPath, options);
      const output = this.formatFileList(files, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'browse',
          path: dirPath,
          fileCount: files.filter(f => f.type === 'file').length,
          directoryCount: files.filter(f => f.type === 'directory').length,
          totalSize: files.reduce((sum, f) => sum + f.size, 0),
          files
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to browse directory: ${error.message}`
      };
    }
  }

  private async generateDirectoryTree(dirPath: string, options: any): Promise<ToolResult> {
    try {
      const tree = await this.buildDirectoryTree(dirPath, options, 0);
      const output = this.formatDirectoryTree(tree, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'tree',
          path: dirPath,
          tree
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to generate directory tree: ${error.message}`
      };
    }
  }

  private async searchFiles(dirPath: string, pattern: string, options: any): Promise<ToolResult> {
    try {
      const searchResults = await this.performFileSearch(dirPath, pattern, options);
      const output = this.formatSearchResults(searchResults, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'search',
          path: dirPath,
          pattern,
          resultCount: searchResults.length,
          results: searchResults
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `File search failed: ${error.message}`
      };
    }
  }

  private async getFileInfo(filePath: string, format: string): Promise<ToolResult> {
    try {
      const fileInfo = await this.getDetailedFileInfo(filePath);
      const output = this.formatFileInfo(fileInfo, format);

      return {
        success: true,
        output,
        metadata: {
          action: 'info',
          fileInfo
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to get file info: ${error.message}`
      };
    }
  }

  private async previewFile(filePath: string, lines: number, format: string): Promise<ToolResult> {
    try {
      const preview = await this.generateFilePreview(filePath, lines);
      const output = this.formatFilePreview(preview, format);

      return {
        success: true,
        output,
        metadata: {
          action: 'preview',
          filePath,
          preview
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to preview file: ${error.message}`
      };
    }
  }

  private async compareFiles(path1: string, path2: string, format: string): Promise<ToolResult> {
    try {
      const comparison = await this.performFileComparison(path1, path2);
      const output = this.formatFileComparison(comparison, format);

      return {
        success: true,
        output,
        metadata: {
          action: 'compare',
          path1,
          path2,
          comparison
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `File comparison failed: ${error.message}`
      };
    }
  }

  private async watchDirectory(dirPath: string, options: any): Promise<ToolResult> {
    try {
      // This would set up file system watching
      // For now, return a placeholder response
      return {
        success: true,
        output: `Started watching directory: ${dirPath}`,
        metadata: {
          action: 'watch',
          path: dirPath,
          watching: true
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to watch directory: ${error.message}`
      };
    }
  }

  private async analyzeDirectory(dirPath: string, options: any): Promise<ToolResult> {
    try {
      const analysis = await this.performDirectoryAnalysis(dirPath, options);
      const output = this.formatDirectoryAnalysis(analysis, options.format);

      return {
        success: true,
        output,
        metadata: {
          action: 'analyze',
          path: dirPath,
          analysis
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Directory analysis failed: ${error.message}`
      };
    }
  }

  private async getDirectoryContents(dirPath: string, options: any): Promise<FileInfo[]> {
    const entries = await readdir(dirPath, { withFileTypes: true });
    const files: FileInfo[] = [];

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      // Skip hidden files if not requested
      if (!options.show_hidden && entry.name.startsWith('.')) {
        continue;
      }

      try {
        const stats = await stat(fullPath);
        const fileInfo: FileInfo = {
          name: entry.name,
          path: fullPath,
          type: entry.isDirectory() ? 'directory' : entry.isSymbolicLink() ? 'symlink' : 'file',
          size: stats.size,
          modified: stats.mtime,
          permissions: this.getPermissionsString(stats.mode),
          extension: entry.isFile() ? path.extname(entry.name).toLowerCase() : undefined,
          isHidden: entry.name.startsWith('.'),
          isExecutable: !!(stats.mode & parseInt('111', 8)),
          mimeType: entry.isFile() ? this.getMimeType(entry.name) : undefined
        };

        // Apply type filter
        if (this.matchesTypeFilter(fileInfo, options.filter_type)) {
          files.push(fileInfo);
        }
      } catch (error) {
        // Skip files that can't be accessed
        continue;
      }
    }

    return this.sortFiles(files, options.sort_by, options.sort_order);
  }

  private async buildDirectoryTree(dirPath: string, options: any, currentDepth: number): Promise<DirectoryTree> {
    if (currentDepth >= options.max_depth) {
      return {
        name: path.basename(dirPath),
        path: dirPath,
        type: 'directory',
        children: [],
        size: 0,
        fileCount: 0,
        directoryCount: 0
      };
    }

    const entries = await readdir(dirPath, { withFileTypes: true });
    const children: (DirectoryTree | FileInfo)[] = [];
    let totalSize = 0;
    let fileCount = 0;
    let directoryCount = 0;

    for (const entry of entries) {
      if (!options.show_hidden && entry.name.startsWith('.')) {
        continue;
      }

      const fullPath = path.join(dirPath, entry.name);

      try {
        if (entry.isDirectory()) {
          if (options.recursive) {
            const subTree = await this.buildDirectoryTree(fullPath, options, currentDepth + 1);
            children.push(subTree);
            totalSize += subTree.size;
            fileCount += subTree.fileCount;
            directoryCount += subTree.directoryCount + 1;
          } else {
            directoryCount++;
          }
        } else {
          const stats = await stat(fullPath);
          const fileInfo: FileInfo = {
            name: entry.name,
            path: fullPath,
            type: 'file',
            size: stats.size,
            modified: stats.mtime,
            permissions: this.getPermissionsString(stats.mode),
            extension: path.extname(entry.name).toLowerCase(),
            isHidden: entry.name.startsWith('.'),
            isExecutable: !!(stats.mode & parseInt('111', 8)),
            mimeType: this.getMimeType(entry.name)
          };
          children.push(fileInfo);
          totalSize += stats.size;
          fileCount++;
        }
      } catch (error) {
        // Skip inaccessible files/directories
        continue;
      }
    }

    return {
      name: path.basename(dirPath),
      path: dirPath,
      type: 'directory',
      children,
      size: totalSize,
      fileCount,
      directoryCount
    };
  }

  private async performFileSearch(dirPath: string, pattern: string, options: any): Promise<FileInfo[]> {
    const globPattern = options.recursive ? `**/${pattern}` : pattern;
    const files = await glob(globPattern, {
      cwd: dirPath,
      absolute: true,
      nodir: options.filter_type === 'files',
      dot: options.show_hidden
    });

    const results: FileInfo[] = [];

    for (const file of files) {
      try {
        const stats = await stat(file);
        const fileInfo: FileInfo = {
          name: path.basename(file),
          path: file,
          type: stats.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          modified: stats.mtime,
          permissions: this.getPermissionsString(stats.mode),
          extension: stats.isFile() ? path.extname(file).toLowerCase() : undefined,
          isHidden: path.basename(file).startsWith('.'),
          isExecutable: !!(stats.mode & parseInt('111', 8)),
          mimeType: stats.isFile() ? this.getMimeType(file) : undefined
        };

        if (this.matchesTypeFilter(fileInfo, options.filter_type)) {
          results.push(fileInfo);
        }
      } catch (error) {
        // Skip inaccessible files
        continue;
      }
    }

    return results;
  }

  private async getDetailedFileInfo(filePath: string): Promise<any> {
    const stats = await stat(filePath);
    const isDirectory = stats.isDirectory();

    const info: any = {
      path: filePath,
      name: path.basename(filePath),
      directory: path.dirname(filePath),
      type: isDirectory ? 'directory' : 'file',
      size: stats.size,
      sizeFormatted: this.formatFileSize(stats.size),
      created: stats.birthtime,
      modified: stats.mtime,
      accessed: stats.atime,
      permissions: this.getPermissionsString(stats.mode),
      owner: stats.uid,
      group: stats.gid,
      isHidden: path.basename(filePath).startsWith('.'),
      isExecutable: !!(stats.mode & parseInt('111', 8)),
      extension: isDirectory ? null : path.extname(filePath).toLowerCase(),
      mimeType: isDirectory ? null : this.getMimeType(filePath)
    };

    if (isDirectory) {
      const contents = await readdir(filePath);
      info.contents = {
        total: contents.length,
        files: contents.filter(name => {
          try {
            return fs.statSync(path.join(filePath, name)).isFile();
          } catch { return false; }
        }).length,
        directories: contents.filter(name => {
          try {
            return fs.statSync(path.join(filePath, name)).isDirectory();
          } catch { return false; }
        }).length
      };
    }

    return info;
  }

  private async generateFilePreview(filePath: string, lines: number): Promise<any> {
    const stats = await stat(filePath);

    if (stats.isDirectory()) {
      return {
        type: 'directory',
        message: 'Cannot preview directory contents'
      };
    }

    if (stats.size > 1024 * 1024) { // 1MB limit
      return {
        type: 'large_file',
        message: 'File too large for preview',
        size: stats.size
      };
    }

    const extension = path.extname(filePath).toLowerCase();
    const mimeType = this.getMimeType(filePath);

    if (this.isBinaryFile(extension, mimeType)) {
      return {
        type: 'binary',
        message: 'Binary file cannot be previewed',
        extension,
        mimeType
      };
    }

    try {
      const content = await readFile(filePath, 'utf-8');
      const fileLines = content.split('\n');
      const preview = fileLines.slice(0, lines).join('\n');

      return {
        type: 'text',
        content: preview,
        totalLines: fileLines.length,
        previewLines: Math.min(lines, fileLines.length),
        truncated: fileLines.length > lines,
        encoding: 'utf-8',
        size: stats.size
      };
    } catch (error: any) {
      return {
        type: 'error',
        message: 'Failed to read file content',
        error: error.message
      };
    }
  }

  private async performFileComparison(path1: string, path2: string): Promise<any> {
    const [stats1, stats2] = await Promise.all([
      stat(path1).catch(() => null),
      stat(path2).catch(() => null)
    ]);

    const comparison: any = {
      path1,
      path2,
      exists1: !!stats1,
      exists2: !!stats2,
      identical: false,
      differences: []
    };

    if (!stats1 || !stats2) {
      comparison.differences.push(
        !stats1 ? `${path1} does not exist` : `${path2} does not exist`
      );
      return comparison;
    }

    // Compare basic properties
    if (stats1.size !== stats2.size) {
      comparison.differences.push(`Size differs: ${stats1.size} vs ${stats2.size}`);
    }

    if (Math.abs(stats1.mtime.getTime() - stats2.mtime.getTime()) > 1000) {
      comparison.differences.push(`Modified time differs: ${stats1.mtime} vs ${stats2.mtime}`);
    }

    if (stats1.isDirectory() !== stats2.isDirectory()) {
      comparison.differences.push(`Type differs: ${stats1.isDirectory() ? 'directory' : 'file'} vs ${stats2.isDirectory() ? 'directory' : 'file'}`);
    }

    // For files, compare content if they're small enough
    if (stats1.isFile() && stats2.isFile() && stats1.size < 1024 * 1024 && stats2.size < 1024 * 1024) {
      try {
        const [content1, content2] = await Promise.all([
          readFile(path1, 'utf-8'),
          readFile(path2, 'utf-8')
        ]);

        if (content1 === content2) {
          comparison.identical = true;
        } else {
          comparison.differences.push('File contents differ');
          // Could add line-by-line diff here
        }
      } catch (error) {
        comparison.differences.push('Could not compare file contents');
      }
    }

    return comparison;
  }

  private async performDirectoryAnalysis(dirPath: string, options: any): Promise<any> {
    const analysis: any = {
      path: dirPath,
      totalSize: 0,
      fileCount: 0,
      directoryCount: 0,
      fileTypes: new Map<string, number>(),
      largestFiles: [],
      oldestFiles: [],
      newestFiles: [],
      duplicates: [],
      emptyDirectories: []
    };

    const files = await this.getAllFiles(dirPath, options);

    for (const file of files) {
      if (file.type === 'file') {
        analysis.fileCount++;
        analysis.totalSize += file.size;

        const ext = file.extension || 'no extension';
        analysis.fileTypes.set(ext, (analysis.fileTypes.get(ext) || 0) + 1);
      } else if (file.type === 'directory') {
        analysis.directoryCount++;
      }
    }

    // Sort files for analysis
    const sortedBySize = files.filter(f => f.type === 'file').sort((a, b) => b.size - a.size);
    const sortedByDate = files.filter(f => f.type === 'file').sort((a, b) => a.modified.getTime() - b.modified.getTime());

    analysis.largestFiles = sortedBySize.slice(0, 10);
    analysis.oldestFiles = sortedByDate.slice(0, 10);
    analysis.newestFiles = sortedByDate.slice(-10).reverse();

    return analysis;
  }

  private async getAllFiles(dirPath: string, options: any): Promise<FileInfo[]> {
    const allFiles: FileInfo[] = [];

    const processDirectory = async (currentPath: string, depth: number) => {
      if (depth >= options.max_depth) return;

      try {
        const entries = await readdir(currentPath, { withFileTypes: true });

        for (const entry of entries) {
          if (!options.show_hidden && entry.name.startsWith('.')) continue;

          const fullPath = path.join(currentPath, entry.name);
          const stats = await stat(fullPath);

          const fileInfo: FileInfo = {
            name: entry.name,
            path: fullPath,
            type: entry.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime,
            permissions: this.getPermissionsString(stats.mode),
            extension: entry.isFile() ? path.extname(entry.name).toLowerCase() : undefined,
            isHidden: entry.name.startsWith('.'),
            isExecutable: !!(stats.mode & parseInt('111', 8)),
            mimeType: entry.isFile() ? this.getMimeType(entry.name) : undefined
          };

          allFiles.push(fileInfo);

          if (entry.isDirectory() && options.recursive) {
            await processDirectory(fullPath, depth + 1);
          }
        }
      } catch (error) {
        // Skip inaccessible directories
      }
    };

    await processDirectory(dirPath, 0);
    return allFiles;
  }

  private getPermissionsString(mode: number): string {
    const permissions = [];

    // Owner permissions
    permissions.push((mode & parseInt('400', 8)) ? 'r' : '-');
    permissions.push((mode & parseInt('200', 8)) ? 'w' : '-');
    permissions.push((mode & parseInt('100', 8)) ? 'x' : '-');

    // Group permissions
    permissions.push((mode & parseInt('040', 8)) ? 'r' : '-');
    permissions.push((mode & parseInt('020', 8)) ? 'w' : '-');
    permissions.push((mode & parseInt('010', 8)) ? 'x' : '-');

    // Other permissions
    permissions.push((mode & parseInt('004', 8)) ? 'r' : '-');
    permissions.push((mode & parseInt('002', 8)) ? 'w' : '-');
    permissions.push((mode & parseInt('001', 8)) ? 'x' : '-');

    return permissions.join('');
  }

  private getMimeType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.js': 'application/javascript',
      '.ts': 'application/typescript',
      '.json': 'application/json',
      '.html': 'text/html',
      '.css': 'text/css',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.pdf': 'application/pdf',
      '.zip': 'application/zip',
      '.tar': 'application/x-tar',
      '.gz': 'application/gzip'
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }

  private isBinaryFile(extension: string, mimeType: string): boolean {
    const textExtensions = ['.txt', '.js', '.ts', '.json', '.html', '.css', '.md', '.xml', '.yaml', '.yml', '.log'];
    const textMimeTypes = ['text/', 'application/json', 'application/javascript', 'application/typescript'];

    if (textExtensions.includes(extension)) return false;
    if (textMimeTypes.some(type => mimeType.startsWith(type))) return false;

    return true;
  }

  private matchesTypeFilter(fileInfo: FileInfo, filterType: string): boolean {
    switch (filterType) {
      case 'files':
        return fileInfo.type === 'file';
      case 'directories':
        return fileInfo.type === 'directory';
      case 'images':
        return fileInfo.type === 'file' && fileInfo.mimeType?.startsWith('image/') || false;
      case 'documents':
        return fileInfo.type === 'file' && ['.pdf', '.doc', '.docx', '.txt', '.md'].includes(fileInfo.extension || '');
      case 'code':
        return fileInfo.type === 'file' && ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb'].includes(fileInfo.extension || '');
      case 'archives':
        return fileInfo.type === 'file' && ['.zip', '.tar', '.gz', '.rar', '.7z'].includes(fileInfo.extension || '');
      default:
        return true;
    }
  }

  private sortFiles(files: FileInfo[], sortBy: string, sortOrder: string): FileInfo[] {
    const sorted = [...files].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'modified':
          comparison = a.modified.getTime() - b.modified.getTime();
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'extension':
          comparison = (a.extension || '').localeCompare(b.extension || '');
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return sorted;
  }

  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  private formatFileList(files: FileInfo[], format: string): string {
    if (format === 'json') {
      return JSON.stringify(files, null, 2);
    }

    if (format === 'table') {
      return this.formatAsTable(files);
    }

    // List format
    let output = `Directory Contents (${files.length} items)\n`;
    output += '='.repeat(50) + '\n\n';

    for (const file of files) {
      const typeIcon = file.type === 'directory' ? '📁' : '📄';
      const sizeStr = file.type === 'file' ? this.formatFileSize(file.size) : '<DIR>';
      const modifiedStr = file.modified.toLocaleDateString();

      output += `${typeIcon} ${file.name.padEnd(30)} ${sizeStr.padStart(10)} ${modifiedStr}\n`;
    }

    return output;
  }

  private formatAsTable(files: FileInfo[]): string {
    const headers = ['Type', 'Name', 'Size', 'Modified', 'Permissions'];
    const rows = files.map(file => [
      file.type === 'directory' ? 'DIR' : 'FILE',
      file.name,
      file.type === 'file' ? this.formatFileSize(file.size) : '<DIR>',
      file.modified.toLocaleDateString(),
      file.permissions
    ]);

    // Simple table formatting
    let output = headers.join(' | ') + '\n';
    output += headers.map(() => '---').join(' | ') + '\n';

    for (const row of rows) {
      output += row.join(' | ') + '\n';
    }

    return output;
  }

  private formatDirectoryTree(tree: DirectoryTree, format: string): string {
    if (format === 'json') {
      return JSON.stringify(tree, null, 2);
    }

    const formatNode = (node: DirectoryTree | FileInfo, prefix: string = '', isLast: boolean = true): string => {
      const connector = isLast ? '└── ' : '├── ';
      const icon = node.type === 'directory' ? '📁' : '📄';
      let result = `${prefix}${connector}${icon} ${node.name}`;

      if (node.type === 'file') {
        result += ` (${this.formatFileSize(node.size)})`;
      } else if ('children' in node) {
        result += ` (${node.fileCount} files, ${node.directoryCount} dirs)`;
      }

      result += '\n';

      if ('children' in node && node.children.length > 0) {
        const newPrefix = prefix + (isLast ? '    ' : '│   ');
        node.children.forEach((child, index) => {
          const childIsLast = index === node.children.length - 1;
          result += formatNode(child, newPrefix, childIsLast);
        });
      }

      return result;
    };

    return formatNode(tree);
  }

  private formatSearchResults(results: FileInfo[], format: string): string {
    if (format === 'json') {
      return JSON.stringify(results, null, 2);
    }

    let output = `Search Results (${results.length} matches)\n`;
    output += '='.repeat(50) + '\n\n';

    for (const file of results) {
      const typeIcon = file.type === 'directory' ? '📁' : '📄';
      output += `${typeIcon} ${file.path}\n`;
      if (file.type === 'file') {
        output += `   Size: ${this.formatFileSize(file.size)}, Modified: ${file.modified.toLocaleDateString()}\n`;
      }
      output += '\n';
    }

    return output;
  }

  private formatFileInfo(info: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(info, null, 2);
    }

    return `
File Information
================

Path: ${info.path}
Name: ${info.name}
Type: ${info.type}
Size: ${info.sizeFormatted}
Created: ${info.created}
Modified: ${info.modified}
Accessed: ${info.accessed}
Permissions: ${info.permissions}
Hidden: ${info.isHidden ? 'Yes' : 'No'}
Executable: ${info.isExecutable ? 'Yes' : 'No'}
${info.extension ? `Extension: ${info.extension}` : ''}
${info.mimeType ? `MIME Type: ${info.mimeType}` : ''}
${info.contents ? `\nContents: ${info.contents.total} items (${info.contents.files} files, ${info.contents.directories} directories)` : ''}
`;
  }

  private formatFilePreview(preview: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(preview, null, 2);
    }

    if (preview.type === 'text') {
      return `
File Preview (${preview.previewLines}/${preview.totalLines} lines)
${preview.truncated ? '[Content truncated]' : ''}
${'='.repeat(50)}

${preview.content}

${'='.repeat(50)}
Size: ${this.formatFileSize(preview.size)}
Encoding: ${preview.encoding}
`;
    } else {
      return `
File Preview
============

${preview.message}
${preview.size ? `Size: ${this.formatFileSize(preview.size)}` : ''}
${preview.extension ? `Extension: ${preview.extension}` : ''}
${preview.mimeType ? `MIME Type: ${preview.mimeType}` : ''}
`;
    }
  }

  private formatFileComparison(comparison: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(comparison, null, 2);
    }

    return `
File Comparison
===============

Path 1: ${comparison.path1} ${comparison.exists1 ? '✓' : '✗'}
Path 2: ${comparison.path2} ${comparison.exists2 ? '✓' : '✗'}

Identical: ${comparison.identical ? 'Yes' : 'No'}

${comparison.differences.length > 0 ? 'Differences:\n' + comparison.differences.map((d: any) => `  - ${d}`).join('\n') : 'No differences found'}
`;
  }

  private formatDirectoryAnalysis(analysis: any, format: string): string {
    if (format === 'json') {
      return JSON.stringify(analysis, null, 2);
    }

    const fileTypesArray = Array.from(analysis.fileTypes.entries())
      .sort((a: any, b: any) => b[1] - a[1])
      .slice(0, 10);

    return `
Directory Analysis
==================

Path: ${analysis.path}
Total Size: ${this.formatFileSize(analysis.totalSize)}
Files: ${analysis.fileCount}
Directories: ${analysis.directoryCount}

File Types:
${fileTypesArray.map((entry: any) => `  ${entry[0]}: ${entry[1]} files`).join('\n')}

Largest Files:
${analysis.largestFiles.slice(0, 5).map((file: FileInfo) => `  ${file.name}: ${this.formatFileSize(file.size)}`).join('\n')}

Oldest Files:
${analysis.oldestFiles.slice(0, 5).map((file: FileInfo) => `  ${file.name}: ${file.modified.toLocaleDateString()}`).join('\n')}

Newest Files:
${analysis.newestFiles.slice(0, 5).map((file: FileInfo) => `  ${file.name}: ${file.modified.toLocaleDateString()}`).join('\n')}
`;
  }
}
