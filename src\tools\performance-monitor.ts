import { exec } from 'child_process';
import { promisify } from 'util';
import { EventEmitter } from 'events';
import * as os from 'os';
import * as fs from 'fs';
import { Tool, ToolResult } from '../types';

const execAsync = promisify(exec);

interface PerformanceMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    loadAverage: number[];
    cores: number;
    model: string;
    speed: number;
    temperature?: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    available: number;
    percentage: number;
    swap: {
      total: number;
      used: number;
      free: number;
    };
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percentage: number;
    readSpeed: number;
    writeSpeed: number;
    iops: number;
  };
  network: {
    interfaces: NetworkInterface[];
    totalBytesReceived: number;
    totalBytesSent: number;
    packetsReceived: number;
    packetsSent: number;
    errors: number;
    dropped: number;
  };
  processes: {
    total: number;
    running: number;
    sleeping: number;
    zombie: number;
    topCPU: ProcessInfo[];
    topMemory: ProcessInfo[];
  };
  system: {
    uptime: number;
    bootTime: Date;
    platform: string;
    arch: string;
    hostname: string;
    users: number;
  };
}

interface NetworkInterface {
  name: string;
  type: string;
  speed: number;
  bytesReceived: number;
  bytesSent: number;
  packetsReceived: number;
  packetsSent: number;
  errors: number;
  dropped: number;
  isUp: boolean;
}

interface ProcessInfo {
  pid: number;
  name: string;
  cpu: number;
  memory: number;
  user: string;
  command: string;
}

interface PerformanceAlert {
  id: string;
  type: 'cpu' | 'memory' | 'disk' | 'network' | 'process';
  threshold: number;
  operator: 'gt' | 'lt' | 'eq';
  enabled: boolean;
  message: string;
  actions: string[];
}

export class PerformanceMonitorTool extends EventEmitter implements Tool {
  name = 'performance_monitor';
  description = 'Advanced real-time performance monitoring with alerts, benchmarks, and optimization suggestions';

  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['metrics', 'monitor', 'benchmark', 'alerts', 'optimize', 'report', 'compare', 'profile'],
        description: 'Performance monitoring action to perform'
      },
      duration: {
        type: 'number',
        description: 'Monitoring duration in seconds (default: 60)'
      },
      interval: {
        type: 'number',
        description: 'Sampling interval in seconds (default: 1)'
      },
      format: {
        type: 'string',
        enum: ['json', 'table', 'chart', 'summary', 'detailed'],
        description: 'Output format (default: summary)'
      },
      component: {
        type: 'string',
        enum: ['cpu', 'memory', 'disk', 'network', 'processes', 'all'],
        description: 'Component to monitor (default: all)'
      },
      alert_config: {
        type: 'object',
        description: 'Alert configuration for thresholds'
      },
      benchmark_type: {
        type: 'string',
        enum: ['cpu', 'memory', 'disk', 'network', 'comprehensive'],
        description: 'Type of benchmark to run'
      },
      process_name: {
        type: 'string',
        description: 'Specific process name to monitor'
      },
      save_to_file: {
        type: 'string',
        description: 'File path to save monitoring data'
      }
    },
    required: ['action']
  };

  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring: boolean = false;
  private metricsHistory: PerformanceMetrics[] = [];
  private alerts: Map<string, PerformanceAlert> = new Map();
  private baselineMetrics: PerformanceMetrics | null = null;

  async execute(args: {
    action: string;
    duration?: number;
    interval?: number;
    format?: string;
    component?: string;
    alert_config?: any;
    benchmark_type?: string;
    process_name?: string;
    save_to_file?: string;
  }): Promise<ToolResult> {
    try {
      const {
        action,
        duration = 60,
        interval = 1,
        format = 'summary',
        component = 'all',
        alert_config,
        benchmark_type = 'comprehensive',
        process_name,
        save_to_file
      } = args;

      switch (action) {
        case 'metrics':
          return await this.getCurrentMetrics(component, format);

        case 'monitor':
          return await this.startMonitoring(duration, interval, component, format, save_to_file);

        case 'benchmark':
          return await this.runBenchmark(benchmark_type, format);

        case 'alerts':
          return this.manageAlerts(alert_config, format);

        case 'optimize':
          return await this.generateOptimizationSuggestions(format);

        case 'report':
          return this.generatePerformanceReport(format);

        case 'compare':
          return this.comparePerformance(format);

        case 'profile':
          return await this.profileProcess(process_name, duration, format);

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Performance monitoring failed'
      };
    }
  }

  private async getCurrentMetrics(component: string, format: string): Promise<ToolResult> {
    try {
      const metrics = await this.collectMetrics();
      const filteredMetrics = this.filterMetricsByComponent(metrics, component);
      const output = this.formatMetrics(filteredMetrics, format);

      return {
        success: true,
        output,
        metadata: {
          action: 'current_metrics',
          component,
          metrics: filteredMetrics,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to collect metrics: ${error.message}`
      };
    }
  }

  private async startMonitoring(duration: number, interval: number, component: string, format: string, saveToFile?: string): Promise<ToolResult> {
    if (this.isMonitoring) {
      return {
        success: false,
        output: '',
        error: 'Monitoring is already active'
      };
    }

    this.isMonitoring = true;
    this.metricsHistory = [];

    const maxSamples = Math.floor(duration / interval);
    let samplesCollected = 0;

    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.collectMetrics();
        this.metricsHistory.push(metrics);
        this.checkAlerts(metrics);

        samplesCollected++;
        this.emit('metrics', metrics);

        if (samplesCollected >= maxSamples) {
          this.stopMonitoring();
        }
      } catch (error) {
        this.emit('error', error);
      }
    }, interval * 1000);

    // Set timeout to stop monitoring
    setTimeout(() => {
      if (this.isMonitoring) {
        this.stopMonitoring();
      }
    }, duration * 1000);

    // Save to file if requested
    if (saveToFile) {
      setTimeout(() => {
        this.saveMetricsToFile(saveToFile);
      }, (duration + 1) * 1000);
    }

    return {
      success: true,
      output: `Performance monitoring started (duration: ${duration}s, interval: ${interval}s, component: ${component})`,
      metadata: {
        action: 'start_monitoring',
        duration,
        interval,
        component,
        maxSamples
      }
    };
  }

  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    this.emit('monitoringStopped', {
      samplesCollected: this.metricsHistory.length,
      duration: this.metricsHistory.length > 0 ?
        new Date(this.metricsHistory[this.metricsHistory.length - 1].timestamp).getTime() -
        new Date(this.metricsHistory[0].timestamp).getTime() : 0
    });
  }

  private async runBenchmark(benchmarkType: string, format: string): Promise<ToolResult> {
    try {
      const benchmarkResults = await this.performBenchmark(benchmarkType);
      const output = this.formatBenchmarkResults(benchmarkResults, format);

      return {
        success: true,
        output,
        metadata: {
          action: 'benchmark',
          benchmarkType,
          results: benchmarkResults
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Benchmark failed: ${error.message}`
      };
    }
  }

  private async collectMetrics(): Promise<PerformanceMetrics> {
    const timestamp = new Date().toISOString();

    const [cpuMetrics, memoryMetrics, diskMetrics, networkMetrics, processMetrics, systemMetrics] = await Promise.all([
      this.getCPUMetrics(),
      this.getMemoryMetrics(),
      this.getDiskMetrics(),
      this.getNetworkMetrics(),
      this.getProcessMetrics(),
      this.getSystemMetrics()
    ]);

    return {
      timestamp,
      cpu: cpuMetrics,
      memory: memoryMetrics,
      disk: diskMetrics,
      network: networkMetrics,
      processes: processMetrics,
      system: systemMetrics
    };
  }

  private async getCPUMetrics(): Promise<PerformanceMetrics['cpu']> {
    const cpus = os.cpus();
    const loadAvg = os.loadavg();

    // Calculate CPU usage
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    });

    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - ~~(100 * idle / total);

    // Try to get CPU temperature (Linux only)
    let temperature: number | undefined;
    try {
      if (process.platform === 'linux') {
        const tempData = await fs.promises.readFile('/sys/class/thermal/thermal_zone0/temp', 'utf8');
        temperature = parseInt(tempData) / 1000; // Convert from millidegrees
      }
    } catch (error) {
      // Temperature not available
    }

    return {
      usage,
      loadAverage: loadAvg,
      cores: cpus.length,
      model: cpus[0]?.model || 'Unknown',
      speed: cpus[0]?.speed || 0,
      temperature
    };
  }

  private async getMemoryMetrics(): Promise<PerformanceMetrics['memory']> {
    const total = os.totalmem();
    const free = os.freemem();
    const used = total - free;
    const percentage = (used / total) * 100;

    // Get swap information (Linux/macOS)
    let swapTotal = 0;
    let swapUsed = 0;
    let swapFree = 0;

    try {
      if (process.platform === 'linux') {
        const { stdout } = await execAsync('cat /proc/meminfo | grep -E "SwapTotal|SwapFree"');
        const lines = stdout.split('\n');
        for (const line of lines) {
          if (line.includes('SwapTotal')) {
            swapTotal = parseInt(line.split(/\s+/)[1]) * 1024; // Convert from kB
          } else if (line.includes('SwapFree')) {
            swapFree = parseInt(line.split(/\s+/)[1]) * 1024; // Convert from kB
          }
        }
        swapUsed = swapTotal - swapFree;
      }
    } catch (error) {
      // Swap info not available
    }

    return {
      total,
      used,
      free,
      available: free,
      percentage,
      swap: {
        total: swapTotal,
        used: swapUsed,
        free: swapFree
      }
    };
  }

  private async getDiskMetrics(): Promise<PerformanceMetrics['disk']> {
    try {
      const command = process.platform === 'win32'
        ? 'wmic logicaldisk get size,freespace,caption /value'
        : 'df -B1 / | tail -1';

      const { stdout } = await execAsync(command);

      let total = 0, free = 0, used = 0;

      if (process.platform === 'win32') {
        const lines = stdout.split('\n').filter(line => line.includes('='));
        for (const line of lines) {
          if (line.includes('Size=')) {
            total += parseInt(line.split('=')[1]) || 0;
          } else if (line.includes('FreeSpace=')) {
            free += parseInt(line.split('=')[1]) || 0;
          }
        }
        used = total - free;
      } else {
        const parts = stdout.trim().split(/\s+/);
        total = parseInt(parts[1]) || 0;
        used = parseInt(parts[2]) || 0;
        free = parseInt(parts[3]) || 0;
      }

      const percentage = total > 0 ? (used / total) * 100 : 0;

      // Mock disk I/O metrics (would need more complex implementation for real values)
      return {
        total,
        used,
        free,
        percentage,
        readSpeed: 0,
        writeSpeed: 0,
        iops: 0
      };
    } catch (error) {
      return {
        total: 0,
        used: 0,
        free: 0,
        percentage: 0,
        readSpeed: 0,
        writeSpeed: 0,
        iops: 0
      };
    }
  }

  private async getNetworkMetrics(): Promise<PerformanceMetrics['network']> {
    const interfaces: NetworkInterface[] = [];
    let totalBytesReceived = 0;
    let totalBytesSent = 0;
    let packetsReceived = 0;
    let packetsSent = 0;
    let errors = 0;
    let dropped = 0;

    try {
      const networkInterfaces = os.networkInterfaces();

      for (const [name, addrs] of Object.entries(networkInterfaces)) {
        if (addrs) {
          const isUp = addrs.some(addr => !addr.internal);
          interfaces.push({
            name,
            type: 'ethernet', // Simplified
            speed: 1000, // Mock value
            bytesReceived: 0, // Would need platform-specific implementation
            bytesSent: 0,
            packetsReceived: 0,
            packetsSent: 0,
            errors: 0,
            dropped: 0,
            isUp
          });
        }
      }
    } catch (error) {
      // Network info not available
    }

    return {
      interfaces,
      totalBytesReceived,
      totalBytesSent,
      packetsReceived,
      packetsSent,
      errors,
      dropped
    };
  }

  private async getProcessMetrics(): Promise<PerformanceMetrics['processes']> {
    try {
      const command = process.platform === 'win32'
        ? 'tasklist /fo csv'
        : 'ps aux';

      const { stdout } = await execAsync(command);
      const lines = stdout.split('\n').filter(line => line.trim());

      const total = lines.length - 1; // Exclude header
      const running = total; // Simplified
      const sleeping = 0;
      const zombie = 0;

      // Parse top processes (simplified)
      const topCPU: ProcessInfo[] = [];
      const topMemory: ProcessInfo[] = [];

      // This would need more sophisticated parsing for real implementation
      for (let i = 1; i < Math.min(6, lines.length); i++) {
        const line = lines[i];
        if (process.platform === 'win32') {
          const parts = line.split(',');
          if (parts.length >= 5) {
            topCPU.push({
              pid: parseInt(parts[1]?.replace(/"/g, '')) || 0,
              name: parts[0]?.replace(/"/g, '') || 'Unknown',
              cpu: 0, // Would need calculation
              memory: parseInt(parts[4]?.replace(/[^0-9]/g, '')) || 0,
              user: 'Unknown',
              command: parts[0]?.replace(/"/g, '') || 'Unknown'
            });
          }
        }
      }

      topMemory.push(...topCPU); // Simplified

      return {
        total,
        running,
        sleeping,
        zombie,
        topCPU,
        topMemory
      };
    } catch (error) {
      return {
        total: 0,
        running: 0,
        sleeping: 0,
        zombie: 0,
        topCPU: [],
        topMemory: []
      };
    }
  }

  private async getSystemMetrics(): Promise<PerformanceMetrics['system']> {
    const uptime = os.uptime();
    const bootTime = new Date(Date.now() - uptime * 1000);

    let users = 0;
    try {
      if (process.platform !== 'win32') {
        const { stdout } = await execAsync('who | wc -l');
        users = parseInt(stdout.trim()) || 0;
      }
    } catch (error) {
      // Users count not available
    }

    return {
      uptime,
      bootTime,
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      users
    };
  }

  private filterMetricsByComponent(metrics: PerformanceMetrics, component: string): any {
    if (component === 'all') {
      return metrics;
    }

    return {
      timestamp: metrics.timestamp,
      [component]: metrics[component as keyof PerformanceMetrics]
    };
  }

  private formatMetrics(metrics: any, format: string): string {
    switch (format) {
      case 'json':
        return JSON.stringify(metrics, null, 2);

      case 'table':
        return this.formatMetricsAsTable(metrics);

      case 'chart':
        return this.formatMetricsAsChart(metrics);

      case 'detailed':
        return this.formatDetailedMetrics(metrics);

      case 'summary':
      default:
        return this.formatMetricsSummary(metrics);
    }
  }

  private formatMetricsSummary(metrics: any): string {
    let output = `📊 Performance Metrics (${metrics.timestamp})\n`;
    output += '='.repeat(60) + '\n\n';

    if (metrics.cpu) {
      output += `🖥️  CPU Usage: ${metrics.cpu.usage.toFixed(1)}%\n`;
      output += `   Cores: ${metrics.cpu.cores}, Model: ${metrics.cpu.model}\n`;
      output += `   Load Average: ${metrics.cpu.loadAverage.map((l: number) => l.toFixed(2)).join(', ')}\n`;
      if (metrics.cpu.temperature) {
        output += `   Temperature: ${metrics.cpu.temperature.toFixed(1)}°C\n`;
      }
      output += '\n';
    }

    if (metrics.memory) {
      output += `💾 Memory Usage: ${metrics.memory.percentage.toFixed(1)}%\n`;
      output += `   Used: ${this.formatBytes(metrics.memory.used)} / ${this.formatBytes(metrics.memory.total)}\n`;
      output += `   Available: ${this.formatBytes(metrics.memory.available)}\n`;
      if (metrics.memory.swap.total > 0) {
        output += `   Swap: ${this.formatBytes(metrics.memory.swap.used)} / ${this.formatBytes(metrics.memory.swap.total)}\n`;
      }
      output += '\n';
    }

    if (metrics.disk) {
      output += `💿 Disk Usage: ${metrics.disk.percentage.toFixed(1)}%\n`;
      output += `   Used: ${this.formatBytes(metrics.disk.used)} / ${this.formatBytes(metrics.disk.total)}\n`;
      output += `   Free: ${this.formatBytes(metrics.disk.free)}\n`;
      output += '\n';
    }

    if (metrics.network) {
      output += `🌐 Network Interfaces: ${metrics.network.interfaces.length}\n`;
      const activeInterfaces = metrics.network.interfaces.filter((iface: NetworkInterface) => iface.isUp);
      output += `   Active: ${activeInterfaces.length}\n`;
      output += '\n';
    }

    if (metrics.processes) {
      output += `⚙️  Processes: ${metrics.processes.total} total (${metrics.processes.running} running)\n`;
      if (metrics.processes.topCPU.length > 0) {
        output += `   Top CPU: ${metrics.processes.topCPU[0].name} (${metrics.processes.topCPU[0].cpu.toFixed(1)}%)\n`;
      }
      output += '\n';
    }

    if (metrics.system) {
      output += `🖥️  System Uptime: ${this.formatUptime(metrics.system.uptime)}\n`;
      output += `   Platform: ${metrics.system.platform} (${metrics.system.arch})\n`;
      output += `   Hostname: ${metrics.system.hostname}\n`;
      if (metrics.system.users > 0) {
        output += `   Users: ${metrics.system.users}\n`;
      }
    }

    return output;
  }

  private formatMetricsAsTable(metrics: any): string {
    // Simplified table format
    let output = '┌─────────────────┬──────────────────────────────────────┐\n';
    output += '│ Metric          │ Value                                │\n';
    output += '├─────────────────┼──────────────────────────────────────┤\n';

    if (metrics.cpu) {
      output += `│ CPU Usage       │ ${metrics.cpu.usage.toFixed(1).padStart(35)}% │\n`;
    }
    if (metrics.memory) {
      output += `│ Memory Usage    │ ${metrics.memory.percentage.toFixed(1).padStart(35)}% │\n`;
    }
    if (metrics.disk) {
      output += `│ Disk Usage      │ ${metrics.disk.percentage.toFixed(1).padStart(35)}% │\n`;
    }

    output += '└─────────────────┴──────────────────────────────────────┘\n';
    return output;
  }

  private formatMetricsAsChart(metrics: any): string {
    let output = '📈 Performance Chart\n';
    output += '='.repeat(50) + '\n\n';

    if (metrics.cpu) {
      output += this.createSimpleChart('CPU Usage', metrics.cpu.usage, 100);
    }
    if (metrics.memory) {
      output += this.createSimpleChart('Memory Usage', metrics.memory.percentage, 100);
    }
    if (metrics.disk) {
      output += this.createSimpleChart('Disk Usage', metrics.disk.percentage, 100);
    }

    return output;
  }

  private createSimpleChart(label: string, value: number, max: number): string {
    const barLength = 40;
    const filledLength = Math.round((value / max) * barLength);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);

    return `${label.padEnd(15)} │${bar}│ ${value.toFixed(1)}%\n`;
  }

  private formatDetailedMetrics(metrics: any): string {
    return `📊 Detailed Performance Metrics\n${'='.repeat(60)}\n\n${JSON.stringify(metrics, null, 2)}`;
  }

  private formatBytes(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  }

  private checkAlerts(metrics: PerformanceMetrics): void {
    for (const alert of this.alerts.values()) {
      if (!alert.enabled) continue;

      let value: number;
      switch (alert.type) {
        case 'cpu':
          value = metrics.cpu.usage;
          break;
        case 'memory':
          value = metrics.memory.percentage;
          break;
        case 'disk':
          value = metrics.disk.percentage;
          break;
        default:
          continue;
      }

      let triggered = false;
      switch (alert.operator) {
        case 'gt':
          triggered = value > alert.threshold;
          break;
        case 'lt':
          triggered = value < alert.threshold;
          break;
        case 'eq':
          triggered = Math.abs(value - alert.threshold) < 0.1;
          break;
      }

      if (triggered) {
        this.emit('alert', {
          alert,
          value,
          metrics,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  private manageAlerts(alertConfig: any, format: string): ToolResult {
    // Simplified alert management
    return {
      success: true,
      output: 'Alert management not fully implemented yet',
      metadata: {
        action: 'manage_alerts',
        alertConfig
      }
    };
  }

  private async generateOptimizationSuggestions(format: string): Promise<ToolResult> {
    const metrics = await this.collectMetrics();
    const suggestions: string[] = [];

    if (metrics.cpu.usage > 80) {
      suggestions.push('🔥 High CPU usage detected. Consider closing unnecessary applications or upgrading hardware.');
    }

    if (metrics.memory.percentage > 85) {
      suggestions.push('💾 High memory usage detected. Consider closing memory-intensive applications or adding more RAM.');
    }

    if (metrics.disk.percentage > 90) {
      suggestions.push('💿 Disk space is running low. Consider cleaning up files or expanding storage.');
    }

    if (suggestions.length === 0) {
      suggestions.push('✅ System performance looks good! No immediate optimizations needed.');
    }

    const output = format === 'json'
      ? JSON.stringify({ suggestions, metrics }, null, 2)
      : `🚀 Performance Optimization Suggestions\n${'='.repeat(50)}\n\n${suggestions.join('\n\n')}`;

    return {
      success: true,
      output,
      metadata: {
        action: 'optimization_suggestions',
        suggestions,
        metrics
      }
    };
  }

  private generatePerformanceReport(format: string): ToolResult {
    if (this.metricsHistory.length === 0) {
      return {
        success: false,
        output: '',
        error: 'No metrics history available. Run monitoring first.'
      };
    }

    const report = this.analyzeMetricsHistory();
    const output = format === 'json'
      ? JSON.stringify(report, null, 2)
      : this.formatPerformanceReport(report);

    return {
      success: true,
      output,
      metadata: {
        action: 'performance_report',
        report,
        samplesAnalyzed: this.metricsHistory.length
      }
    };
  }

  private analyzeMetricsHistory(): any {
    const cpuUsages = this.metricsHistory.map(m => m.cpu.usage);
    const memoryUsages = this.metricsHistory.map(m => m.memory.percentage);
    const diskUsages = this.metricsHistory.map(m => m.disk.percentage);

    return {
      timeRange: {
        start: this.metricsHistory[0].timestamp,
        end: this.metricsHistory[this.metricsHistory.length - 1].timestamp,
        duration: this.metricsHistory.length
      },
      cpu: {
        average: cpuUsages.reduce((a, b) => a + b, 0) / cpuUsages.length,
        min: Math.min(...cpuUsages),
        max: Math.max(...cpuUsages),
        current: cpuUsages[cpuUsages.length - 1]
      },
      memory: {
        average: memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length,
        min: Math.min(...memoryUsages),
        max: Math.max(...memoryUsages),
        current: memoryUsages[memoryUsages.length - 1]
      },
      disk: {
        average: diskUsages.reduce((a, b) => a + b, 0) / diskUsages.length,
        min: Math.min(...diskUsages),
        max: Math.max(...diskUsages),
        current: diskUsages[diskUsages.length - 1]
      }
    };
  }

  private formatPerformanceReport(report: any): string {
    return `
📊 Performance Analysis Report
==============================

Time Range: ${report.timeRange.start} to ${report.timeRange.end}
Duration: ${report.timeRange.duration} samples

CPU Performance:
  Current: ${report.cpu.current.toFixed(1)}%
  Average: ${report.cpu.average.toFixed(1)}%
  Min: ${report.cpu.min.toFixed(1)}%
  Max: ${report.cpu.max.toFixed(1)}%

Memory Performance:
  Current: ${report.memory.current.toFixed(1)}%
  Average: ${report.memory.average.toFixed(1)}%
  Min: ${report.memory.min.toFixed(1)}%
  Max: ${report.memory.max.toFixed(1)}%

Disk Performance:
  Current: ${report.disk.current.toFixed(1)}%
  Average: ${report.disk.average.toFixed(1)}%
  Min: ${report.disk.min.toFixed(1)}%
  Max: ${report.disk.max.toFixed(1)}%
`;
  }

  private comparePerformance(format: string): ToolResult {
    // Simplified comparison
    return {
      success: true,
      output: 'Performance comparison not fully implemented yet',
      metadata: {
        action: 'compare_performance'
      }
    };
  }

  private async profileProcess(processName?: string, duration: number = 30, format: string = 'summary'): Promise<ToolResult> {
    // Simplified process profiling
    return {
      success: true,
      output: `Process profiling for ${processName || 'all processes'} not fully implemented yet`,
      metadata: {
        action: 'profile_process',
        processName,
        duration
      }
    };
  }

  private async performBenchmark(benchmarkType: string): Promise<any> {
    // Simplified benchmark implementation
    return {
      type: benchmarkType,
      score: Math.random() * 1000,
      details: 'Benchmark implementation in progress'
    };
  }

  private formatBenchmarkResults(results: any, format: string): string {
    return format === 'json'
      ? JSON.stringify(results, null, 2)
      : `🏃 Benchmark Results (${results.type})\nScore: ${results.score.toFixed(2)}\n${results.details}`;
  }

  private async saveMetricsToFile(filePath: string): Promise<void> {
    try {
      const data = JSON.stringify(this.metricsHistory, null, 2);
      await fs.promises.writeFile(filePath, data, 'utf8');
      this.emit('fileSaved', { filePath, samples: this.metricsHistory.length });
    } catch (error) {
      this.emit('error', error);
    }
  }
}
