import * as fs from 'fs';
import * as path from 'path';
import { Tool, ToolResult } from '../types';

export class RefactorTool implements Tool {
  name = 'refactor';
  description = 'Advanced code refactoring and generation tool with AI-powered suggestions';

  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['extract_function', 'rename_variable', 'add_comments', 'optimize_imports', 'generate_tests', 'create_class', 'add_error_handling'],
        description: 'Type of refactoring action to perform'
      },
      file_path: {
        type: 'string',
        description: 'Path to the file to refactor'
      },
      target: {
        type: 'string',
        description: 'Target code block, function name, or variable name'
      },
      new_name: {
        type: 'string',
        description: 'New name for rename operations'
      },
      start_line: {
        type: 'number',
        description: 'Start line number for code selection'
      },
      end_line: {
        type: 'number',
        description: 'End line number for code selection'
      },
      template: {
        type: 'string',
        description: 'Template or pattern for code generation'
      },
      options: {
        type: 'object',
        description: 'Additional options for refactoring'
      }
    },
    required: ['action', 'file_path']
  };

  async execute(args: {
    action: string;
    file_path: string;
    target?: string;
    new_name?: string;
    start_line?: number;
    end_line?: number;
    template?: string;
    options?: any;
  }): Promise<ToolResult> {
    try {
      const { action, file_path, target, new_name, start_line, end_line, template, options = {} } = args;

      if (!fs.existsSync(file_path)) {
        throw new Error(`File not found: ${file_path}`);
      }

      const content = fs.readFileSync(file_path, 'utf8');
      const language = this.detectLanguage(file_path);

      switch (action) {
        case 'extract_function':
          return await this.extractFunction(content, file_path, start_line, end_line, target, language);

        case 'rename_variable':
          return await this.renameVariable(content, file_path, target, new_name, language);

        case 'add_comments':
          return await this.addComments(content, file_path, language, options);

        case 'optimize_imports':
          return await this.optimizeImports(content, file_path, language);

        case 'generate_tests':
          return await this.generateTests(content, file_path, target, language, template);

        case 'create_class':
          return await this.createClass(file_path, target, template, language, options);

        case 'add_error_handling':
          return await this.addErrorHandling(content, file_path, language, options);

        default:
          throw new Error(`Unknown refactoring action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Refactoring operation failed'
      };
    }
  }

  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.ts': 'typescript',
      '.js': 'javascript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php'
    };
    return languageMap[ext] || 'unknown';
  }

  private async extractFunction(
    content: string,
    filePath: string,
    startLine?: number,
    endLine?: number,
    functionName?: string,
    language?: string
  ): Promise<ToolResult> {
    const lines = content.split('\n');

    if (!startLine || !endLine) {
      throw new Error('Start and end line numbers are required for function extraction');
    }

    if (startLine < 1 || endLine > lines.length || startLine > endLine) {
      throw new Error('Invalid line range specified');
    }

    const extractedCode = lines.slice(startLine - 1, endLine).join('\n');
    const newFunctionName = functionName || 'extractedFunction';

    // Generate function based on language
    let newFunction = '';
    let functionCall = '';

    if (language === 'typescript' || language === 'javascript') {
      newFunction = `\nfunction ${newFunctionName}() {\n${extractedCode}\n}\n`;
      functionCall = `${newFunctionName}();`;
    } else if (language === 'python') {
      newFunction = `\ndef ${newFunctionName}():\n${extractedCode.split('\n').map(line => '    ' + line).join('\n')}\n`;
      functionCall = `${newFunctionName}()`;
    } else {
      newFunction = `\n// Extracted function\n${extractedCode}\n`;
      functionCall = `// Call ${newFunctionName}`;
    }

    // Replace extracted code with function call
    const newLines = [
      ...lines.slice(0, startLine - 1),
      functionCall,
      ...lines.slice(endLine)
    ];

    // Add the new function at the end
    newLines.push(newFunction);

    const newContent = newLines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');

    return {
      success: true,
      output: `Function '${newFunctionName}' extracted successfully`,
      metadata: {
        action: 'extract_function',
        functionName: newFunctionName,
        linesExtracted: endLine - startLine + 1,
        originalLines: startLine + '-' + endLine
      }
    };
  }

  private async renameVariable(
    content: string,
    filePath: string,
    oldName?: string,
    newName?: string,
    language?: string
  ): Promise<ToolResult> {
    if (!oldName || !newName) {
      throw new Error('Both old name and new name are required for variable renaming');
    }

    // Create regex pattern for variable renaming (word boundaries)
    const regex = new RegExp(`\\b${oldName}\\b`, 'g');
    const matches = content.match(regex);

    if (!matches) {
      return {
        success: true,
        output: `No occurrences of '${oldName}' found`,
        metadata: { action: 'rename_variable', replacements: 0 }
      };
    }

    const newContent = content.replace(regex, newName);
    fs.writeFileSync(filePath, newContent, 'utf8');

    return {
      success: true,
      output: `Renamed '${oldName}' to '${newName}' (${matches.length} occurrences)`,
      metadata: {
        action: 'rename_variable',
        oldName,
        newName,
        replacements: matches.length
      }
    };
  }

  private async addComments(
    content: string,
    filePath: string,
    language?: string,
    options?: any
  ): Promise<ToolResult> {
    const lines = content.split('\n');
    const newLines: string[] = [];
    let commentsAdded = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      // Add comments for functions
      if (this.isFunctionDeclaration(trimmed, language)) {
        const functionName = this.extractFunctionName(trimmed, language);
        const comment = this.generateFunctionComment(functionName, language);
        newLines.push(comment);
        commentsAdded++;
      }

      // Add comments for complex logic
      if (this.isComplexLogic(trimmed, language) && !this.hasComment(lines, i)) {
        const comment = this.generateLogicComment(trimmed, language);
        newLines.push(comment);
        commentsAdded++;
      }

      newLines.push(line);
    }

    const newContent = newLines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');

    return {
      success: true,
      output: `Added ${commentsAdded} comments to improve code documentation`,
      metadata: {
        action: 'add_comments',
        commentsAdded,
        language
      }
    };
  }

  private async optimizeImports(
    content: string,
    filePath: string,
    language?: string
  ): Promise<ToolResult> {
    const lines = content.split('\n');
    const imports: string[] = [];
    const otherLines: string[] = [];
    let optimized = 0;

    // Separate imports from other code
    for (const line of lines) {
      const trimmed = line.trim();
      if (this.isImportStatement(trimmed, language)) {
        imports.push(line);
      } else {
        otherLines.push(line);
      }
    }

    // Sort and deduplicate imports
    const uniqueImports = [...new Set(imports)];
    uniqueImports.sort();
    optimized = imports.length - uniqueImports.length;

    // Reconstruct content
    const newContent = [...uniqueImports, '', ...otherLines].join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');

    return {
      success: true,
      output: `Optimized imports: removed ${optimized} duplicates, sorted ${uniqueImports.length} imports`,
      metadata: {
        action: 'optimize_imports',
        originalImports: imports.length,
        optimizedImports: uniqueImports.length,
        duplicatesRemoved: optimized
      }
    };
  }

  private async generateTests(
    content: string,
    filePath: string,
    target?: string,
    language?: string,
    template?: string
  ): Promise<ToolResult> {
    const functions = this.extractFunctions(content, language);
    const testFileName = this.generateTestFileName(filePath, language);

    let testContent = this.generateTestFileHeader(language, template);
    let testsGenerated = 0;

    for (const func of functions) {
      if (!target || func.name === target) {
        testContent += this.generateTestCase(func, language);
        testsGenerated++;
      }
    }

    fs.writeFileSync(testFileName, testContent, 'utf8');

    return {
      success: true,
      output: `Generated ${testsGenerated} test cases in ${testFileName}`,
      metadata: {
        action: 'generate_tests',
        testFile: testFileName,
        testsGenerated,
        targetFunction: target
      }
    };
  }

  private async createClass(
    filePath: string,
    className?: string,
    template?: string,
    language?: string,
    options?: any
  ): Promise<ToolResult> {
    if (!className) {
      throw new Error('Class name is required');
    }

    const classContent = this.generateClassTemplate(className, language, template, options);
    const newFilePath = this.generateClassFileName(filePath, className, language);

    fs.writeFileSync(newFilePath, classContent, 'utf8');

    return {
      success: true,
      output: `Created class '${className}' in ${newFilePath}`,
      metadata: {
        action: 'create_class',
        className,
        filePath: newFilePath,
        language
      }
    };
  }

  private async addErrorHandling(
    content: string,
    filePath: string,
    language?: string,
    options?: any
  ): Promise<ToolResult> {
    const lines = content.split('\n');
    const newLines: string[] = [];
    let errorHandlingAdded = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      // Add try-catch for risky operations
      if (this.isRiskyOperation(trimmed, language) && !this.hasErrorHandling(lines, i)) {
        const errorHandling = this.generateErrorHandling(trimmed, language);
        newLines.push(errorHandling.before);
        newLines.push(line);
        newLines.push(errorHandling.after);
        errorHandlingAdded++;
      } else {
        newLines.push(line);
      }
    }

    const newContent = newLines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');

    return {
      success: true,
      output: `Added error handling to ${errorHandlingAdded} risky operations`,
      metadata: {
        action: 'add_error_handling',
        errorHandlingAdded,
        language
      }
    };
  }

  // Helper methods for code analysis and generation
  private isFunctionDeclaration(line: string, language?: string): boolean {
    if (language === 'typescript' || language === 'javascript') {
      return /^(function\s+\w+|const\s+\w+\s*=|class\s+\w+|export\s+(function|const))/.test(line);
    } else if (language === 'python') {
      return /^def\s+\w+/.test(line);
    }
    return false;
  }

  private extractFunctionName(line: string, language?: string): string {
    if (language === 'typescript' || language === 'javascript') {
      const match = line.match(/(?:function\s+(\w+)|const\s+(\w+)\s*=|class\s+(\w+))/);
      return match ? (match[1] || match[2] || match[3]) : 'unknown';
    } else if (language === 'python') {
      const match = line.match(/def\s+(\w+)/);
      return match ? match[1] : 'unknown';
    }
    return 'unknown';
  }

  private generateFunctionComment(functionName: string, language?: string): string {
    if (language === 'typescript' || language === 'javascript') {
      return `/**\n * ${functionName} - Description needed\n * @returns Description needed\n */`;
    } else if (language === 'python') {
      return `    """\n    ${functionName} - Description needed\n    Returns: Description needed\n    """`;
    }
    return `// ${functionName} - Description needed`;
  }

  private isComplexLogic(line: string, language?: string): boolean {
    return /\b(if|while|for|switch|try)\b/.test(line);
  }

  private hasComment(lines: string[], index: number): boolean {
    if (index > 0) {
      const prevLine = lines[index - 1].trim();
      return prevLine.startsWith('//') || prevLine.startsWith('/*') || prevLine.startsWith('*') || prevLine.startsWith('"""');
    }
    return false;
  }

  private generateLogicComment(line: string, language?: string): string {
    if (language === 'python') {
      return '    # TODO: Add description for this logic';
    }
    return '    // TODO: Add description for this logic';
  }

  private isImportStatement(line: string, language?: string): boolean {
    if (language === 'typescript' || language === 'javascript') {
      return /^import\s+/.test(line) || /^const\s+.*=\s*require\(/.test(line);
    } else if (language === 'python') {
      return /^(import\s+|from\s+.*import)/.test(line);
    }
    return false;
  }

  private extractFunctions(content: string, language?: string): Array<{name: string, params: string[], body: string}> {
    // Simplified function extraction - would need more sophisticated parsing for production
    const functions: Array<{name: string, params: string[], body: string}> = [];
    const lines = content.split('\n');

    for (const line of lines) {
      if (this.isFunctionDeclaration(line, language)) {
        const name = this.extractFunctionName(line, language);
        functions.push({
          name,
          params: [], // Would need proper parsing
          body: line
        });
      }
    }

    return functions;
  }

  private generateTestFileName(filePath: string, language?: string): string {
    const dir = path.dirname(filePath);
    const name = path.basename(filePath, path.extname(filePath));
    const ext = path.extname(filePath);

    if (language === 'python') {
      return path.join(dir, `test_${name}.py`);
    }
    return path.join(dir, `${name}.test${ext}`);
  }

  private generateTestFileHeader(language?: string, template?: string): string {
    if (template) {
      return template + '\n\n';
    }

    if (language === 'typescript' || language === 'javascript') {
      return `import { describe, it, expect } from '@jest/globals';\n\n`;
    } else if (language === 'python') {
      return `import unittest\nfrom ${path.basename(process.cwd())} import *\n\nclass TestCase(unittest.TestCase):\n\n`;
    }
    return '// Generated test file\n\n';
  }

  private generateTestCase(func: {name: string, params: string[], body: string}, language?: string): string {
    if (language === 'typescript' || language === 'javascript') {
      return `describe('${func.name}', () => {\n  it('should work correctly', () => {\n    // TODO: Implement test\n    expect(${func.name}()).toBeDefined();\n  });\n});\n\n`;
    } else if (language === 'python') {
      return `    def test_${func.name}(self):\n        # TODO: Implement test\n        self.assertIsNotNone(${func.name}())\n\n`;
    }
    return `// Test for ${func.name}\n\n`;
  }

  private generateClassTemplate(className: string, language?: string, template?: string, options?: any): string {
    if (template) {
      return template.replace(/\{\{className\}\}/g, className);
    }

    if (language === 'typescript') {
      return `export class ${className} {\n  constructor() {\n    // TODO: Implement constructor\n  }\n\n  // TODO: Add methods\n}\n`;
    } else if (language === 'javascript') {
      return `class ${className} {\n  constructor() {\n    // TODO: Implement constructor\n  }\n\n  // TODO: Add methods\n}\n\nmodule.exports = ${className};\n`;
    } else if (language === 'python') {
      return `class ${className}:\n    def __init__(self):\n        # TODO: Implement constructor\n        pass\n\n    # TODO: Add methods\n`;
    }
    return `// ${className} class\n`;
  }

  private generateClassFileName(basePath: string, className: string, language?: string): string {
    const dir = path.dirname(basePath);
    const ext = path.extname(basePath);
    return path.join(dir, `${className}${ext}`);
  }

  private isRiskyOperation(line: string, language?: string): boolean {
    return /\b(fetch|axios|fs\.|readFile|writeFile|JSON\.parse|eval)\b/.test(line);
  }

  private hasErrorHandling(lines: string[], index: number): boolean {
    // Check if already inside try-catch block
    for (let i = Math.max(0, index - 10); i < Math.min(lines.length, index + 10); i++) {
      if (lines[i].trim().startsWith('try') || lines[i].trim().startsWith('catch')) {
        return true;
      }
    }
    return false;
  }

  private generateErrorHandling(line: string, language?: string): {before: string, after: string} {
    if (language === 'typescript' || language === 'javascript') {
      return {
        before: '    try {',
        after: '    } catch (error) {\n      console.error("Error:", error);\n    }'
      };
    } else if (language === 'python') {
      return {
        before: '    try:',
        after: '    except Exception as e:\n        print(f"Error: {e}")'
      };
    }
    return {
      before: '    // Error handling needed',
      after: '    // End error handling'
    };
  }
}
