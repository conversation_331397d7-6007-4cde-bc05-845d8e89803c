import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import { Tool, ToolResult } from '../types';

const execAsync = promisify(exec);

export class ShellTool implements Tool {
  name = 'shell';
  description = 'Execute shell commands in the local environment. Can run any bash/cmd command and return the output.';
  parameters = {
    type: 'object' as const,
    properties: {
      command: {
        type: 'string',
        description: 'The shell command to execute'
      },
      cwd: {
        type: 'string',
        description: 'Working directory for the command (optional)'
      },
      timeout: {
        type: 'number',
        description: 'Timeout in milliseconds (default: 30000)'
      },
      env: {
        type: 'object',
        description: 'Environment variables to set (optional)'
      },
      verbose: {
        type: 'boolean',
        description: 'Show full output without truncation (default: false)'
      },
      format: {
        type: 'string',
        enum: ['raw', 'formatted', 'json'],
        description: 'Output format (default: formatted)'
      },
      interactive: {
        type: 'boolean',
        description: 'Run command in interactive mode (default: false)'
      }
    },
    required: ['command']
  };

  async execute(args: {
    command: string;
    cwd?: string;
    timeout?: number;
    env?: Record<string, string>;
    verbose?: boolean;
    format?: string;
    interactive?: boolean;
  }): Promise<ToolResult> {
    try {
      let { command, cwd, timeout = 30000, env, verbose = false, format = 'formatted', interactive = false } = args;

      // Security check - prevent dangerous commands
      if (this.isDangerousCommand(command)) {
        return {
          success: false,
          output: '',
          error: 'Command blocked for security reasons'
        };
      }

      // Auto-translate common Unix commands to Windows equivalents
      command = this.translateCommandForPlatform(command);

      // Use interactive mode if requested
      if (interactive) {
        return await this.executeInteractive({
          command,
          cwd,
          env
        });
      }

      const options: any = {
        timeout,
        maxBuffer: verbose ? 1024 * 1024 * 50 : 1024 * 1024 * 10, // 50MB for verbose, 10MB normal
        encoding: 'utf8'
      };

      if (cwd) {
        options.cwd = cwd;
      }

      if (env) {
        options.env = { ...process.env, ...env };
      }

      const { stdout, stderr } = await execAsync(command, options);
      const rawOutput = String(stdout || stderr || 'Command executed successfully');

      // Format output based on requested format
      const formattedOutput = this.formatOutput(rawOutput, format, command);

      return {
        success: true,
        output: formattedOutput,
        metadata: {
          command,
          originalCommand: args.command,
          cwd: cwd || process.cwd(),
          exitCode: 0,
          verbose,
          format,
          outputLength: rawOutput.length,
          platform: process.platform
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: error.stdout || '',
        error: error.stderr || error.message || 'Command execution failed',
        metadata: {
          command: args.command,
          cwd: args.cwd || process.cwd(),
          exitCode: error.code || 1
        }
      };
    }
  }

  async executeInteractive(args: {
    command: string;
    cwd?: string;
    env?: Record<string, string>;
    onOutput?: (data: string) => void;
    onError?: (data: string) => void;
  }): Promise<ToolResult> {
    return new Promise((resolve) => {
      const { command, cwd, env, onOutput, onError } = args;

      if (this.isDangerousCommand(command)) {
        resolve({
          success: false,
          output: '',
          error: 'Command blocked for security reasons'
        });
        return;
      }

      const [cmd, ...cmdArgs] = command.split(' ');
      const options: any = {};

      if (cwd) {
        options.cwd = cwd;
      }

      if (env) {
        options.env = { ...process.env, ...env };
      }

      const child = spawn(cmd, cmdArgs, options);
      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        stdout += output;
        onOutput?.(output);
      });

      child.stderr?.on('data', (data: Buffer) => {
        const error = data.toString();
        stderr += error;
        onError?.(error);
      });

      child.on('close', (code) => {
        resolve({
          success: code === 0,
          output: stdout,
          error: stderr,
          metadata: {
            command,
            cwd: cwd || process.cwd(),
            exitCode: code || 0
          }
        });
      });

      child.on('error', (error) => {
        resolve({
          success: false,
          output: stdout,
          error: error.message,
          metadata: {
            command,
            cwd: cwd || process.cwd(),
            exitCode: 1
          }
        });
      });
    });
  }

  private translateCommandForPlatform(command: string): string {
    if (process.platform !== 'win32') {
      return command; // No translation needed for Unix-like systems
    }

    // Windows command translations
    const translations: Record<string, string> = {
      'pwd': 'echo %cd%',
      'ls': 'dir',
      'ls -l': 'dir',
      'ls -la': 'dir',
      'ls -al': 'dir',
      'cat': 'type',
      'which': 'where',
      'grep': 'findstr',
      'head': 'more',
      'tail': 'more',
      'touch': 'echo. >',
      'cp': 'copy',
      'mv': 'move',
      'rm': 'del',
      'mkdir': 'md',
      'rmdir': 'rd',
      'clear': 'cls',
      'ps': 'tasklist',
      'kill': 'taskkill /PID',
      'df': 'dir /-c',
      'du': 'dir /s',
      'whoami': 'echo %username%',
      'date': 'echo %date% %time%',
      'env': 'set',
      'export': 'set',
      'history': 'doskey /history'
    };

    // Check for exact matches first
    if (translations[command.toLowerCase()]) {
      return translations[command.toLowerCase()];
    }

    // Check for pattern matches
    let translatedCommand = command;

    // Handle ls with directory
    if (/^ls\s+/.test(command)) {
      translatedCommand = command.replace(/^ls\s+/, 'dir ');
    }

    // Handle cat with file
    if (/^cat\s+/.test(command)) {
      translatedCommand = command.replace(/^cat\s+/, 'type ');
    }

    // Handle grep patterns
    if (/^grep\s+/.test(command)) {
      translatedCommand = command.replace(/^grep\s+/, 'findstr ');
    }

    // Handle which command
    if (/^which\s+/.test(command)) {
      translatedCommand = command.replace(/^which\s+/, 'where ');
    }

    // Handle touch with filename
    if (/^touch\s+/.test(command)) {
      const filename = command.replace(/^touch\s+/, '');
      translatedCommand = `echo. > ${filename}`;
    }

    // Handle cp/copy commands
    if (/^cp\s+/.test(command)) {
      translatedCommand = command.replace(/^cp\s+/, 'copy ');
    }

    // Handle mv/move commands
    if (/^mv\s+/.test(command)) {
      translatedCommand = command.replace(/^mv\s+/, 'move ');
    }

    // Handle rm/del commands
    if (/^rm\s+/.test(command)) {
      translatedCommand = command.replace(/^rm\s+/, 'del ');
    }

    // Handle mkdir commands
    if (/^mkdir\s+/.test(command)) {
      translatedCommand = command.replace(/^mkdir\s+/, 'md ');
    }

    return translatedCommand;
  }

  private formatOutput(output: string, format: string, command: string): string {
    switch (format) {
      case 'raw':
        return output;

      case 'json':
        return JSON.stringify({
          command,
          output: output.split('\n'),
          timestamp: new Date().toISOString(),
          platform: process.platform
        }, null, 2);

      case 'formatted':
      default:
        // Enhanced formatting for common commands
        if (command.includes('dir') || command.includes('ls')) {
          return this.formatDirectoryListing(output);
        } else if (command.includes('ps') || command.includes('tasklist')) {
          return this.formatProcessList(output);
        } else if (command.includes('netstat')) {
          return this.formatNetworkInfo(output);
        }
        return output;
    }
  }

  private formatDirectoryListing(output: string): string {
    const lines = output.split('\n');
    let formatted = '';
    let inFileList = false;

    for (const line of lines) {
      if (line.trim() === '') continue;

      // Windows dir command formatting
      if (process.platform === 'win32') {
        if (line.includes('Directory of')) {
          formatted += `📁 ${line}\n`;
          formatted += '─'.repeat(50) + '\n';
          inFileList = true;
        } else if (line.includes('<DIR>')) {
          const parts = line.trim().split(/\s+/);
          const name = parts[parts.length - 1];
          const date = parts[0];
          const time = parts[1];
          formatted += `📁 ${name.padEnd(30)} ${date} ${time}\n`;
        } else if (inFileList && line.match(/^\d/)) {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 4) {
            const date = parts[0];
            const time = parts[1];
            const size = parts[2];
            const name = parts.slice(3).join(' ');
            formatted += `📄 ${name.padEnd(30)} ${this.formatFileSize(parseInt(size) || 0).padStart(10)} ${date} ${time}\n`;
          }
        } else if (line.includes('File(s)') || line.includes('Dir(s)')) {
          formatted += `\n📊 ${line}\n`;
        } else {
          formatted += line + '\n';
        }
      } else {
        // Unix ls command formatting
        formatted += line + '\n';
      }
    }

    return formatted || output;
  }

  private formatProcessList(output: string): string {
    const lines = output.split('\n');
    let formatted = '🔄 Running Processes\n';
    formatted += '─'.repeat(80) + '\n';

    for (let i = 0; i < Math.min(lines.length, 20); i++) {
      const line = lines[i];
      if (line.trim() && !line.includes('Image Name') && !line.includes('PID')) {
        formatted += `⚙️  ${line}\n`;
      }
    }

    if (lines.length > 20) {
      formatted += `\n... and ${lines.length - 20} more processes\n`;
    }

    return formatted;
  }

  private formatNetworkInfo(output: string): string {
    const lines = output.split('\n');
    let formatted = '🌐 Network Connections\n';
    formatted += '─'.repeat(80) + '\n';

    for (const line of lines) {
      if (line.trim() && line.includes(':')) {
        formatted += `🔗 ${line}\n`;
      }
    }

    return formatted || output;
  }

  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  private isDangerousCommand(command: string): boolean {
    const dangerousPatterns = [
      /rm\s+-rf\s+\//, // rm -rf /
      /rm\s+-rf\s+\*/, // rm -rf *
      /format\s+c:/, // format c:
      /del\s+\/s\s+\/q\s+c:\\/, // del /s /q c:\
      /shutdown/, // shutdown commands
      /reboot/, // reboot commands
      /halt/, // halt commands
      /mkfs/, // filesystem formatting
      /dd\s+if=.*of=\/dev/, // disk writing
      /:(){ :|:& };:/, // fork bomb
      /curl.*\|\s*sh/, // curl | sh
      /wget.*\|\s*sh/, // wget | sh
      /chmod\s+777\s+\//, // chmod 777 /
      /chown.*\//, // chown on root
      /sudo\s+rm/, // sudo rm
      /sudo\s+dd/, // sudo dd
    ];

    return dangerousPatterns.some(pattern => pattern.test(command.toLowerCase()));
  }

  async getSystemInfo(): Promise<ToolResult> {
    const commands = process.platform === 'win32'
      ? ['systeminfo', 'wmic os get caption,version']
      : ['uname -a', 'lsb_release -a || cat /etc/os-release'];

    let output = '';
    for (const cmd of commands) {
      try {
        const result = await this.execute({ command: cmd });
        if (result.success) {
          output += `${cmd}:\n${result.output}\n\n`;
        }
      } catch (error) {
        // Continue with next command
      }
    }

    return {
      success: true,
      output: output || 'System information not available',
      metadata: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version
      }
    };
  }

  async listProcesses(): Promise<ToolResult> {
    const command = process.platform === 'win32' ? 'tasklist' : 'ps aux';
    return await this.execute({ command });
  }

  async getCurrentDirectory(): Promise<ToolResult> {
    // Use proper commands that actually output the current directory
    const command = process.platform === 'win32' ? 'echo %cd%' : 'pwd';
    return await this.execute({ command });
  }

  async listFiles(directory?: string): Promise<ToolResult> {
    const dir = directory || '.';
    const command = process.platform === 'win32'
      ? `dir "${dir}"`
      : `ls -la "${dir}"`;

    return await this.execute({ command, cwd: directory });
  }

  async getEnvironmentVariable(varName: string): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `echo %${varName}%`
      : `echo $${varName}`;

    return await this.execute({ command });
  }

  async setEnvironmentVariable(varName: string, value: string, persistent: boolean = false): Promise<ToolResult> {
    let command: string;

    if (process.platform === 'win32') {
      command = persistent
        ? `setx ${varName} "${value}"`
        : `set ${varName}=${value}`;
    } else {
      command = persistent
        ? `export ${varName}="${value}" && echo 'export ${varName}="${value}"' >> ~/.bashrc`
        : `export ${varName}="${value}"`;
    }

    return await this.execute({ command });
  }

  async getNetworkInfo(): Promise<ToolResult> {
    const commands = process.platform === 'win32'
      ? ['ipconfig /all', 'netstat -an']
      : ['ifconfig -a', 'netstat -tuln'];

    let output = '';
    for (const cmd of commands) {
      try {
        const result = await this.execute({ command: cmd });
        if (result.success) {
          output += `${cmd}:\n${result.output}\n\n`;
        }
      } catch (error) {
        // Continue with next command
      }
    }

    return {
      success: true,
      output: output || 'Network information not available',
      metadata: {
        platform: process.platform,
        timestamp: new Date().toISOString()
      }
    };
  }

  async getDiskUsage(path?: string): Promise<ToolResult> {
    const targetPath = path || (process.platform === 'win32' ? 'C:' : '/');
    const command = process.platform === 'win32'
      ? `dir "${targetPath}" /-c`
      : `df -h "${targetPath}"`;

    return await this.execute({ command });
  }

  async getMemoryUsage(): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? 'wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value'
      : 'free -h';

    const result = await this.execute({ command });

    // Add Node.js process memory info
    const processMemory = process.memoryUsage();
    const memoryInfo = {
      rss: Math.round(processMemory.rss / 1024 / 1024) + ' MB',
      heapTotal: Math.round(processMemory.heapTotal / 1024 / 1024) + ' MB',
      heapUsed: Math.round(processMemory.heapUsed / 1024 / 1024) + ' MB',
      external: Math.round(processMemory.external / 1024 / 1024) + ' MB'
    };

    return {
      success: result.success,
      output: result.output + '\n\nNode.js Process Memory:\n' + JSON.stringify(memoryInfo, null, 2),
      error: result.error,
      metadata: {
        ...result.metadata,
        processMemory
      }
    };
  }

  async getCPUInfo(): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? 'wmic cpu get name,numberofcores,numberoflogicalprocessors /value'
      : 'lscpu || cat /proc/cpuinfo';

    return await this.execute({ command });
  }

  async killProcess(pid: number, force: boolean = false): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? force ? `taskkill /F /PID ${pid}` : `taskkill /PID ${pid}`
      : force ? `kill -9 ${pid}` : `kill ${pid}`;

    return await this.execute({ command });
  }

  async findProcess(name: string): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `tasklist /FI "IMAGENAME eq ${name}*"`
      : `ps aux | grep ${name}`;

    return await this.execute({ command });
  }

  async testNetworkConnection(host: string, port?: number): Promise<ToolResult> {
    let command: string;

    if (process.platform === 'win32') {
      command = port
        ? `Test-NetConnection -ComputerName ${host} -Port ${port}`
        : `ping -n 4 ${host}`;
    } else {
      command = port
        ? `nc -zv ${host} ${port} 2>&1 || telnet ${host} ${port}`
        : `ping -c 4 ${host}`;
    }

    return await this.execute({ command });
  }

  async getOpenPorts(): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? 'netstat -an | findstr LISTENING'
      : 'netstat -tuln | grep LISTEN';

    return await this.execute({ command });
  }

  async createSymlink(target: string, link: string): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `mklink "${link}" "${target}"`
      : `ln -s "${target}" "${link}"`;

    return await this.execute({ command });
  }

  async compressFiles(files: string[], outputFile: string, format: 'zip' | 'tar' | '7z' = 'zip'): Promise<ToolResult> {
    let command: string;
    const fileList = files.map(f => `"${f}"`).join(' ');

    if (process.platform === 'win32') {
      switch (format) {
        case 'zip':
          command = `powershell Compress-Archive -Path ${fileList} -DestinationPath "${outputFile}"`;
          break;
        case '7z':
          command = `7z a "${outputFile}" ${fileList}`;
          break;
        default:
          command = `tar -czf "${outputFile}" ${fileList}`;
      }
    } else {
      switch (format) {
        case 'zip':
          command = `zip -r "${outputFile}" ${fileList}`;
          break;
        case 'tar':
          command = `tar -czf "${outputFile}" ${fileList}`;
          break;
        case '7z':
          command = `7z a "${outputFile}" ${fileList}`;
          break;
        default:
          command = `tar -czf "${outputFile}" ${fileList}`;
      }
    }

    return await this.execute({ command });
  }

  async extractArchive(archiveFile: string, destination?: string): Promise<ToolResult> {
    const dest = destination || '.';
    let command: string;

    const extension = archiveFile.toLowerCase().split('.').pop();

    if (process.platform === 'win32') {
      switch (extension) {
        case 'zip':
          command = `powershell Expand-Archive -Path "${archiveFile}" -DestinationPath "${dest}"`;
          break;
        case '7z':
          command = `7z x "${archiveFile}" -o"${dest}"`;
          break;
        case 'tar':
        case 'gz':
          command = `tar -xzf "${archiveFile}" -C "${dest}"`;
          break;
        default:
          return {
            success: false,
            output: '',
            error: `Unsupported archive format: ${extension}`
          };
      }
    } else {
      switch (extension) {
        case 'zip':
          command = `unzip "${archiveFile}" -d "${dest}"`;
          break;
        case 'tar':
        case 'gz':
          command = `tar -xzf "${archiveFile}" -C "${dest}"`;
          break;
        case '7z':
          command = `7z x "${archiveFile}" -o"${dest}"`;
          break;
        default:
          return {
            success: false,
            output: '',
            error: `Unsupported archive format: ${extension}`
          };
      }
    }

    return await this.execute({ command });
  }

  async getSystemUptime(): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? 'wmic os get lastbootuptime /value'
      : 'uptime';

    const result = await this.execute({ command });

    // Add Node.js process uptime
    const processUptime = process.uptime();
    const uptimeInfo = {
      processUptimeSeconds: processUptime,
      processUptimeFormatted: this.formatUptime(processUptime)
    };

    return {
      success: result.success,
      output: result.output + '\n\nNode.js Process Uptime:\n' + JSON.stringify(uptimeInfo, null, 2),
      error: result.error,
      metadata: {
        ...result.metadata,
        processUptime: uptimeInfo
      }
    };
  }

  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  }

  async searchFiles(pattern: string, directory?: string, options?: {
    recursive?: boolean;
    caseInsensitive?: boolean;
    fileType?: string;
  }): Promise<ToolResult> {
    const dir = directory || '.';
    const opts = options || {};

    let command: string;

    if (process.platform === 'win32') {
      let searchCmd = `dir "${dir}"`;
      if (opts.recursive) searchCmd += ' /s';
      searchCmd += ` /b | findstr`;
      if (opts.caseInsensitive) searchCmd += ' /i';
      searchCmd += ` "${pattern}"`;
      command = searchCmd;
    } else {
      let findCmd = `find "${dir}"`;
      if (!opts.recursive) findCmd += ' -maxdepth 1';
      findCmd += ' -type f';
      if (opts.fileType) findCmd += ` -name "*.${opts.fileType}"`;
      findCmd += ` -name "*${pattern}*"`;
      if (opts.caseInsensitive) findCmd += ' -iname';
      command = findCmd;
    }

    return await this.execute({ command });
  }
}
